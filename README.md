# 🏋️ University Gym Management System

A comprehensive web-based gym management system designed for university fitness centers. This system provides student registration, attendance tracking, class booking, and administrative management features.

## 🌟 Features

### For Students
- **Registration & Login**: Secure student registration with profile management
- **Dashboard**: Personal dashboard with attendance history and class bookings
- **Class Booking**: Browse and book fitness classes
- **Attendance Tracking**: Check-in/check-out functionality
- **Profile Management**: Update personal information and preferences

### For Administrators
- **Admin Dashboard**: Comprehensive overview with statistics and analytics
- **Student Management**: View, edit, and manage student accounts
- **Attendance Reports**: Track gym usage and generate reports
- **Class Management**: Create, edit, and manage fitness classes
- **Facility Management**: Manage gym facilities and equipment

## 🛠️ Technology Stack

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Backend**: Node.js with Express.js
- **Database**: MySQL
- **Authentication**: JWT (JSON Web Tokens)
- **Security**: bcrypt for password hashing, CORS enabled
- **Development**: <PERSON><PERSON><PERSON> for auto-restart

## 📋 Prerequisites

Before you begin, ensure you have the following installed:
- **Node.js** (v14.0.0 or higher)
- **MySQL** (v5.7 or higher)
- **npm** (comes with Node.js)

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone <repository-url>
cd Major Project
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Database Setup
```bash
# Initialize the database
npm run init-db
```

### 4. Environment Configuration
Copy `.env.example` to `.env` and update the database credentials:
```bash
cp .env.example .env
```

Edit `.env` file:
```env
# Database Configuration
DB_HOST=localhost
DB_USER=your_mysql_username
DB_PASSWORD=your_mysql_password
DB_NAME=gym_management
```

### 5. Start the Application
```bash
# Development mode (with auto-restart)
npm run dev

# Production mode
npm start
```

### 6. Access the Application
- **Frontend**: http://localhost:3000
- **Admin Panel**: http://localhost:3000/admin/dashboard.html

## 🔐 Default Admin Credentials

After running the database initialization, use these credentials to access the admin panel:
- **Username**: admin
- **Password**: admin123
- **Email**: <EMAIL>

## 📁 Project Structure

```
Major Project/
├── admin/                  # Admin panel pages
│   └── dashboard.html
├── config/                 # Configuration files
│   ├── database.js        # Database connection
│   └── database.php       # PHP database config (legacy)
├── css/                   # Stylesheets
│   ├── style.css         # Main styles
│   ├── dashboard.css     # Dashboard styles
│   ├── admin.css         # Admin panel styles
│   └── registration.css  # Registration form styles
├── database/              # Database files
│   └── setup.sql         # Database schema and initial data
├── images/               # Static images
├── js/                   # JavaScript files
│   ├── main.js          # Main application logic
│   ├── login.js         # Login functionality
│   ├── registration.js  # Registration form logic
│   ├── dashboard.js     # Student dashboard
│   └── admin_dashboard.js # Admin dashboard
├── middleware/           # Express middleware
│   └── auth.js          # Authentication middleware
├── routes/              # API routes
│   ├── auth.js         # Authentication routes
│   ├── admin.js        # Admin routes
│   ├── bookings.js     # Booking routes
│   └── classes.js      # Class management routes
├── scripts/            # Utility scripts
│   └── init-database.js # Database initialization
├── server.js           # Main server file
├── package.json        # Dependencies and scripts
└── README.md          # This file
```

## 🔧 Available Scripts

```bash
npm start          # Start the production server
npm run dev        # Start development server with nodemon
npm run init-db    # Initialize database with schema and sample data
npm run setup      # Install dependencies and initialize database
```

## 🌐 API Endpoints

### Authentication
- `POST /api/auth/register` - Student registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout

### Admin
- `GET /api/admin/dashboard` - Dashboard statistics
- `GET /api/admin/students` - List all students
- `GET /api/admin/attendance` - Attendance statistics

### Classes & Bookings
- `GET /api/classes` - List available classes
- `POST /api/bookings` - Book a class
- `GET /api/bookings/user/:id` - User's bookings

## 🎨 Customization

### Styling
- Modify CSS files in the `/css` directory
- Main color scheme can be changed in `css/style.css`
- Admin panel styling in `css/admin.css`

### Database Schema
- Modify `database/setup.sql` for schema changes
- Run `npm run init-db` to apply changes

### Features
- Add new routes in the `/routes` directory
- Extend frontend functionality in `/js` files

## 🔒 Security Features

- **Password Hashing**: bcrypt with salt rounds
- **JWT Authentication**: Secure token-based authentication
- **Input Validation**: Server-side validation for all inputs
- **CORS Protection**: Configured for secure cross-origin requests
- **SQL Injection Prevention**: Parameterized queries

## 🐛 Troubleshooting

### Database Connection Issues
1. Ensure MySQL is running
2. Check database credentials in `.env`
3. Verify database exists: `npm run init-db`

### Port Already in Use
```bash
# Windows
netstat -ano | findstr :3000
taskkill /PID <PID> /F

# Linux/Mac
lsof -i :3000
kill -9 <PID>
```

### Module Not Found Errors
```bash
rm -rf node_modules package-lock.json
npm install
```

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📞 Support

For support and questions:
- Create an issue in the repository
- Contact: <EMAIL>

---

**Made with ❤️ for University Fitness Centers**
