@echo off
echo ========================================
echo   Checking System Requirements
echo ========================================
echo.

echo 🔍 Checking if Node.js is installed...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js NOT FOUND
    echo.
    echo 📥 Please install Node.js:
    echo    1. Go to: https://nodejs.org/
    echo    2. Download LTS version
    echo    3. Run installer
    echo    4. Restart computer
    echo.
    set NODE_OK=0
) else (
    echo ✅ Node.js found:
    node --version
    set NODE_OK=1
)

echo.
echo 🔍 Checking if npm is available...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm NOT FOUND
    echo 📥 npm comes with Node.js - please reinstall Node.js
    echo.
    set NPM_OK=0
) else (
    echo ✅ npm found:
    npm --version
    set NPM_OK=1
)

echo.
echo 🔍 Checking if MySQL is installed...
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ MySQL NOT FOUND
    echo.
    echo 📥 Please install MySQL:
    echo    1. Go to: https://dev.mysql.com/downloads/mysql/
    echo    2. Download Community Server
    echo    3. Run installer
    echo    4. Remember your root password
    echo.
    set MYSQL_OK=0
) else (
    echo ✅ MySQL found:
    mysql --version
    set MYSQL_OK=1
)

echo.
echo 🔍 Checking project files...
if exist "package.json" (
    echo ✅ package.json found
    set PACKAGE_OK=1
) else (
    echo ❌ package.json NOT FOUND
    echo 📁 Make sure you're in the correct project folder
    set PACKAGE_OK=0
)

if exist ".env" (
    echo ✅ .env file found
    set ENV_OK=1
) else (
    echo ❌ .env file NOT FOUND
    echo 📁 Configuration file missing
    set ENV_OK=0
)

echo.
echo ========================================
echo           SUMMARY
echo ========================================

if %NODE_OK%==1 if %NPM_OK%==1 if %MYSQL_OK%==1 if %PACKAGE_OK%==1 if %ENV_OK%==1 (
    echo ✅ ALL REQUIREMENTS MET!
    echo.
    echo 🚀 You can now run your project:
    echo    1. npm install
    echo    2. Update .env with MySQL password
    echo    3. npm run init-db
    echo    4. npm run dev
    echo.
    echo 🌐 Then open: http://localhost:3000
) else (
    echo ❌ MISSING REQUIREMENTS
    echo.
    echo 📋 Please install missing items above
    echo 📖 See QUICK_START.md for detailed instructions
)

echo.
pause
