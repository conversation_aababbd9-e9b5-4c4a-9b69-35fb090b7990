@echo off
echo ========================================
echo   MySQL Connection Setup Helper
echo ========================================
echo.

echo 🔍 Checking MySQL installation...
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ MySQL not found!
    echo.
    echo 📥 Please install MySQL first:
    echo    1. Go to: https://dev.mysql.com/downloads/mysql/
    echo    2. Download Community Server
    echo    3. Run installer and set root password
    echo    4. Start MySQL service
    echo.
    echo 📖 See MYSQL_CONNECTION_GUIDE.md for detailed instructions
    pause
    exit /b 1
) else (
    echo ✅ MySQL found:
    mysql --version
)

echo.
echo 🔍 Checking if MySQL service is running...
sc query mysql >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  MySQL service status unknown
    echo 🔧 Trying to start MySQL service...
    net start mysql >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ Could not start MySQL service
        echo 🔧 Please start MySQL manually:
        echo    - Open Services (services.msc)
        echo    - Find MySQL service
        echo    - Right-click → Start
    ) else (
        echo ✅ MySQL service started
    )
) else (
    echo ✅ MySQL service is available
)

echo.
echo 🔧 Testing MySQL connection...
echo.
echo Please enter your MySQL root password:
set /p mysql_password="Password (or press Enter if no password): "

echo.
echo 🔄 Testing connection...
mysql -u root -p%mysql_password% -e "SELECT 1;" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ MySQL connection failed!
    echo.
    echo 🔧 Possible issues:
    echo    - Wrong password
    echo    - MySQL service not running
    echo    - User 'root' doesn't exist
    echo.
    echo 💡 Try connecting manually:
    echo    mysql -u root -p
    echo.
    pause
    exit /b 1
) else (
    echo ✅ MySQL connection successful!
)

echo.
echo 🔧 Updating .env file with your password...
echo.

REM Create backup of .env
if exist ".env" (
    copy ".env" ".env.backup" >nul
    echo 📋 Created backup: .env.backup
)

REM Update .env file
if exist ".env" (
    powershell -Command "(Get-Content .env) -replace 'DB_PASSWORD=.*', 'DB_PASSWORD=%mysql_password%' | Set-Content .env"
    echo ✅ Updated .env file with your MySQL password
) else (
    echo ❌ .env file not found!
    echo 📁 Make sure you're in the correct project folder
    pause
    exit /b 1
)

echo.
echo 🧪 Testing project database connection...
if exist "test-mysql-connection.js" (
    node test-mysql-connection.js
) else (
    echo ⚠️  test-mysql-connection.js not found
    echo 📁 Make sure you're in the project root folder
)

echo.
echo ========================================
echo           SETUP COMPLETE
echo ========================================
echo.
echo ✅ MySQL is connected and configured!
echo.
echo 🚀 Next steps:
echo    1. npm install
echo    2. npm run init-db
echo    3. npm run dev
echo.
echo 🌐 Then open: http://localhost:3000
echo 👤 Admin login: admin / admin123
echo.
pause
