const UserService = require('../services/user.service');
const { ApiError } = require('../utils/errors');
const logger = require('../utils/logger');

class UserController {
    static async register(req, res, next) {
        try {
            const userData = req.body;
            const user = await UserService.createUser(userData);
            
            res.status(201).json({
                success: true,
                message: 'User registered successfully',
                data: {
                    id: user.id,
                    student_id: user.student_id,
                    email: user.email
                }
            });
        } catch (error) {
            next(new ApiError(error.message, error.status));
        }
    }

    static async updateProfile(req, res, next) {
        try {
            const userId = req.user.id;
            const updates = req.body;
            
            const updatedUser = await UserService.updateUser(userId, updates);
            
            res.json({
                success: true,
                message: 'Profile updated successfully',
                data: updatedUser
            });
        } catch (error) {
            next(new ApiError(error.message, error.status));
        }
    }

    static async getDashboardData(req, res, next) {
        try {
            const userId = req.user.id;
            const dashboardData = await UserService.getUserDashboardData(userId);
            
            res.json({
                success: true,
                data: dashboardData
            });
        } catch (error) {
            next(new ApiError(error.message, error.status));
        }
    }

    static async getAttendanceHistory(req, res, next) {
        try {
            const userId = req.user.id;
            const { startDate, endDate } = req.query;
            
            const attendance = await UserService.getUserAttendance(userId, startDate, endDate);
            
            res.json({
                success: true,
                data: attendance
            });
        } catch (error) {
            next(new ApiError(error.message, error.status));
        }
    }
}

module.exports = UserController;
