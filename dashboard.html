<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - University Gym</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="dashboard-body">
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="images/CUK.png" alt="CUK Logo" class="dashboard-logo">
                <h2>UniGym</h2>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li class="active">
                        <a href="#overview"><i class="fas fa-home"></i> Overview</a>
                    </li>
                    <li>
                        <a href="#workouts"><i class="fas fa-dumbbell"></i> Workouts</a>
                    </li>
                    <li>
                        <a href="#attendance"><i class="fas fa-calendar-check"></i> Attendance</a>
                    </li>
                    <li>
                        <a href="#schedule"><i class="fas fa-calendar"></i> Class Schedule</a>
                    </li>
                    <li>
                        <a href="#profile"><i class="fas fa-user"></i> Profile</a>
                    </li>
                    <li>
                        <a href="#facilities"><i class="fas fa-building"></i> Facilities</a>
                    </li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <button id="logoutBtn" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </button>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Top Header -->
            <header class="dashboard-header">
                <div class="header-left">
                    <button id="menuToggle" class="menu-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1>Dashboard</h1>
                </div>
                <div class="header-right">
                    <div class="notifications-dropdown">
                        <button class="notifications-btn">
                            <i class="fas fa-bell"></i>
                            <span class="notification-badge">3</span>
                        </button>
                        <div class="dropdown-content">
                            <h3>Notifications</h3>
                            <div class="notification-item">
                                <div class="notification-icon"><i class="fas fa-check-circle"></i></div>
                                <div class="notification-text">
                                    <p>Your booking was confirmed</p>
                                    <span class="notification-time">Just now</span>
                                </div>
                            </div>
                            <div class="notification-item">
                                <div class="notification-icon"><i class="fas fa-info-circle"></i></div>
                                <div class="notification-text">
                                    <p>New class schedule available</p>
                                    <span class="notification-time">2 hours ago</span>
                                </div>
                            </div>
                            <div class="notification-item">
                                <div class="notification-icon"><i class="fas fa-exclamation-circle"></i></div>
                                <div class="notification-text">
                                    <p>Maintenance scheduled for tomorrow</p>
                                    <span class="notification-time">Yesterday</span>
                                </div>
                            </div>
                            <a href="#notifications" class="view-all">View All Notifications</a>
                        </div>
                    </div>
                    <div class="user-info">
                        <span id="userFullName">Student Name</span>
                        <img src="images/default-avatar.png" alt="Profile" class="user-avatar">
                        <div class="user-dropdown">
                            <a href="#profile"><i class="fas fa-user"></i> My Profile</a>
                            <a href="#settings"><i class="fas fa-cog"></i> Settings</a>
                            <a href="#" id="logoutLink"><i class="fas fa-sign-out-alt"></i> Logout</a>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Dashboard Content -->
            <div class="dashboard-content">
                <!-- Overview Section -->
                <section id="overview" class="dashboard-section active">
                    <div class="welcome-banner">
                        <h2>Welcome back, <span id="userName">Student</span>!</h2>
                        <p class="current-time" id="currentTime"></p>
                    </div>

                    <div class="stats-container">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-info">
                                <h3>Today's Workout</h3>
                                <p id="todayWorkout">0h 0m</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-fire"></i>
                            </div>
                            <div class="stat-info">
                                <h3>Calories Burned</h3>
                                <p id="caloriesBurned">0 kcal</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-calendar-day"></i>
                            </div>
                            <div class="stat-info">
                                <h3>Workout Streak</h3>
                                <p id="workoutStreak">0 days</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-info">
                                <h3>Check-in Status</h3>
                                <p id="checkInStatus">Not Checked In</p>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-row">
                        <div class="dashboard-card activity-chart-card">
                            <div class="card-header">
                                <h3>Weekly Activity</h3>
                                <div class="card-actions">
                                    <button class="card-action-btn">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <canvas id="activityChart"></canvas>
                            </div>
                        </div>
                        <div class="dashboard-card upcoming-classes-card">
                            <div class="card-header">
                                <h3>Upcoming Classes</h3>
                                <div class="card-actions">
                                    <button class="card-action-btn">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="class-item">
                                    <div class="class-time">
                                        <span class="day">MON</span>
                                        <span class="time">10:00</span>
                                    </div>
                                    <div class="class-info">
                                        <h4>Yoga Basics</h4>
                                        <p>Instructor: Sarah Johnson</p>
                                    </div>
                                    <div class="class-status">
                                        <span class="status-badge booked">Booked</span>
                                    </div>
                                </div>
                                <div class="class-item">
                                    <div class="class-time">
                                        <span class="day">WED</span>
                                        <span class="time">15:30</span>
                                    </div>
                                    <div class="class-info">
                                        <h4>HIIT Training</h4>
                                        <p>Instructor: Mike Peterson</p>
                                    </div>
                                    <div class="class-status">
                                        <button class="book-btn">Book</button>
                                    </div>
                                </div>
                                <div class="class-item">
                                    <div class="class-time">
                                        <span class="day">FRI</span>
                                        <span class="time">18:00</span>
                                    </div>
                                    <div class="class-info">
                                        <h4>Strength Training</h4>
                                        <p>Instructor: David Kim</p>
                                    </div>
                                    <div class="class-status">
                                        <button class="book-btn">Book</button>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <a href="#schedule" class="view-all-link">View All Classes</a>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-row">
                        <div class="dashboard-card check-in-card">
                            <div class="card-header">
                                <h3>Today's Check-in</h3>
                            </div>
                            <div class="card-body">
                                <div class="check-in-status">
                                    <div class="status-icon">
                                        <i class="fas fa-clipboard-check"></i>
                                    </div>
                                    <div class="status-text">
                                        <p>You haven't checked in today</p>
                                        <button id="checkInBtn" class="primary-btn">
                                            <i class="fas fa-sign-in-alt"></i> Check In Now
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="dashboard-card announcements-card">
                            <div class="card-header">
                                <h3>Announcements</h3>
                            </div>
                            <div class="card-body">
                                <div class="announcement-item">
                                    <div class="announcement-date">May 15</div>
                                    <div class="announcement-content">
                                        <h4>New Equipment Arrived</h4>
                                        <p>We've added new treadmills and weight machines to the gym floor.</p>
                                    </div>
                                </div>
                                <div class="announcement-item">
                                    <div class="announcement-date">May 10</div>
                                    <div class="announcement-content">
                                        <h4>Holiday Hours</h4>
                                        <p>The gym will have modified hours during the upcoming holiday weekend.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Other sections will be added here -->
                <section id="workouts" class="dashboard-section">
                    <h2>Workouts</h2>
                    <p>Your workout content will appear here.</p>
                </section>

                <section id="attendance" class="dashboard-section">
                    <h2>Attendance History</h2>
                    <p>Your attendance history will appear here.</p>
                </section>

                <section id="schedule" class="dashboard-section">
                    <h2>Class Schedule</h2>
                    <p>Class schedule will appear here.</p>
                </section>

                <section id="profile" class="dashboard-section">
                    <h2>My Profile</h2>
                    <p>Your profile information will appear here.</p>
                </section>

                <section id="facilities" class="dashboard-section">
                    <h2>Facilities</h2>
                    <p>Facility information will appear here.</p>
                </section>
            </div>
        </main>
    </div>

    <script src="js/dashboard.js"></script>
</body>
</html>


