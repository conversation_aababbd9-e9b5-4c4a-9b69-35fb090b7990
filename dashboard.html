<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - University Gym</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary-color: #1a237e;
            --secondary-color: #e74c3c;
            --accent-color: #3498db;
            --light-bg: #f5f8fa;
            --dark-text: #2c3e50;
            --light-text: #ecf0f1;
            --border-radius: 10px;
            --card-shadow: 0 4px 12px rgba(0,0,0,0.1);
            --transition-speed: 0.3s;
        }
        
        /* Base styles */
        .dashboard-body {
            margin: 0;
            padding: 0;
            background-color: var(--light-bg);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--dark-text);
        }
        
        .dashboard-container {
            display: flex;
            min-height: 100vh;
        }
        
        /* Sidebar styles */
        .sidebar {
            width: 260px;
            background: var(--primary-color);
            color: var(--light-text);
            display: flex;
            flex-direction: column;
            transition: all var(--transition-speed) ease;
            position: fixed;
            height: 100vh;
            z-index: 100;
        }
        
        .sidebar-header {
            padding: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .dashboard-logo {
            width: 50px;
            height: 50px;
            object-fit: contain;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
            flex-grow: 1;
        }
        
        .sidebar-nav ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .sidebar-nav li {
            margin: 0.5rem 0;
        }
        
        .sidebar-nav a {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            color: var(--light-text);
            text-decoration: none;
            transition: all var(--transition-speed) ease;
            border-left: 3px solid transparent;
        }
        
        .sidebar-nav a i {
            margin-right: 10px;
            font-size: 1.2rem;
            width: 24px;
            text-align: center;
        }
        
        .sidebar-nav li.active a {
            background: rgba(255,255,255,0.1);
            border-left: 3px solid var(--secondary-color);
        }
        
        .sidebar-nav a:hover {
            background: rgba(255,255,255,0.1);
        }
        
        .sidebar-footer {
            padding: 1rem 1.5rem;
            border-top: 1px solid rgba(255,255,255,0.1);
        }
        
        .logout-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            padding: 0.75rem;
            background: rgba(231, 76, 60, 0.8);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background var(--transition-speed) ease;
        }
        
        .logout-btn:hover {
            background: var(--secondary-color);
        }
        
        .logout-btn i {
            margin-right: 8px;
        }
        
        /* Main content styles */
        .main-content {
            flex: 1;
            margin-left: 260px;
            transition: margin var(--transition-speed) ease;
        }
        
        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 2rem;
            background: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 99;
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
        
        .sidebar-toggle {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--dark-text);
            cursor: pointer;
            margin-right: 1rem;
            display: none;
        }
        
        .header-right {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }
        
        .notifications-dropdown {
            position: relative;
            cursor: pointer;
        }
        
        .notification-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: var(--secondary-color);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .dropdown-content {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            min-width: 300px;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            display: none;
            z-index: 100;
        }
        
        .notifications-dropdown.active .dropdown-content {
            display: block;
        }
        
        .notification-item {
            display: flex;
            padding: 1rem;
            border-bottom: 1px solid #eee;
        }
        
        .notification-icon {
            margin-right: 1rem;
            color: var(--accent-color);
        }
        
        .notification-text p {
            margin: 0 0 0.25rem;
        }
        
        .notification-time {
            font-size: 0.8rem;
            color: #777;
        }
        
        .view-all {
            display: block;
            text-align: center;
            padding: 0.75rem;
            color: var(--accent-color);
            text-decoration: none;
            font-weight: 500;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            position: relative;
            cursor: pointer;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .user-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            min-width: 200px;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            display: none;
            z-index: 100;
        }
        
        .user-info.active .user-dropdown {
            display: block;
        }
        
        .user-dropdown a {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: var(--dark-text);
            text-decoration: none;
            transition: background var(--transition-speed) ease;
        }
        
        .user-dropdown a:hover {
            background: #f5f5f5;
        }
        
        .user-dropdown a i {
            margin-right: 0.75rem;
            width: 20px;
            text-align: center;
        }
        
        /* Dashboard content styles */
        .dashboard-content {
            padding: 2rem;
        }
        
        .dashboard-section {
            display: none;
        }
        
        .dashboard-section.active {
            display: block;
        }
        
        .welcome-banner {
            background: linear-gradient(135deg, var(--primary-color), #303f9f);
            color: white;
            padding: 2rem;
            border-radius: var(--border-radius);
            margin-bottom: 2rem;
        }
        
        .welcome-banner h2 {
            margin: 0 0 0.5rem;
            font-size: 1.8rem;
        }
        
        .current-time {
            margin: 0;
            opacity: 0.8;
        }
        
        .quick-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            display: flex;
            align-items: center;
            box-shadow: var(--card-shadow);
        }
        
        .stat-card i {
            font-size: 2rem;
            color: var(--accent-color);
            margin-right: 1rem;
            background: rgba(52, 152, 219, 0.1);
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }
        
        .stat-card:nth-child(2) i {
            color: #e74c3c;
            background: rgba(231, 76, 60, 0.1);
        }
        
        .stat-card:nth-child(3) i {
            color: #f39c12;
            background: rgba(243, 156, 18, 0.1);
        }
        
        .stat-card:nth-child(4) i {
            color: #2ecc71;
            background: rgba(46, 204, 113, 0.1);
        }
        
        .stat-info h3 {
            margin: 0 0 0.5rem;
            font-size: 1rem;
            color: #777;
        }
        
        .stat-info p {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 1.5rem;
        }
        
        .dashboard-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            overflow: hidden;
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.25rem 1.5rem;
            border-bottom: 1px solid #eee;
        }
        
        .card-header h3 {
            margin: 0;
            font-size: 1.25rem;
        }
        
        .card-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .card-action-btn {
            background: #f5f5f5;
            border: none;
            padding: 0.5rem 0.75rem;
            border-radius: 4px;
            cursor: pointer;
            transition: background var(--transition-speed) ease;
        }
        
        .card-action-btn:hover {
            background: #e0e0e0;
        }
        
        .view-all-link {
            color: var(--accent-color);
            text-decoration: none;
            font-size: 0.9rem;
        }
        
        /* Schedule styles */
        .schedule-list {
            padding: 1.5rem;
        }
        
        .schedule-item {
            display: flex;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid #eee;
        }
        
        .schedule-item:last-child {
            border-bottom: none;
        }
        
        .class-image {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            object-fit: cover;
            margin-right: 15px;
        }
        
        .schedule-time {
            width: 80px;
            font-weight: 600;
            color: var(--accent-color);
        }
        
        .schedule-details {
            flex: 1;
        }
        
        .schedule-details h4 {
            margin: 0 0 0.5rem;
        }
        
        .schedule-details p {
            margin: 0;
            color: #777;
            font-size: 0.9rem;
        }
        
        .instructor-info {
            display: flex;
            align-items: center;
            margin-bottom: 0.25rem;
        }
        
        .instructor-avatar {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 10px;
        }
        
        .schedule-status {
            padding: 0.25rem 0.75rem;
            background: rgba(46, 204, 113, 0.1);
            color: #2ecc71;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        /* Quick actions styles */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            padding: 1.5rem;
        }
        
        .action-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            background: var(--accent-color);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: all var(--transition-speed) ease;
        }
        
        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .action-btn i {
            margin-right: 0.5rem;
        }
        
        .action-btn:nth-child(1) {
            background: #3498db;
        }
        
        .action-btn:nth-child(2) {
            background: #2ecc71;
        }
        
        .action-btn:nth-child(3) {
            background: #f39c12;
        }
        
        .action-btn:nth-child(4) {
            background: #9b59b6;
        }
        
        /* Activity feed styles */
        .activity-feed {
            padding: 1.5rem;
        }
        
        .activity-item {
            display: flex;
            align-items: flex-start;
            padding: 1rem 0;
            border-bottom: 1px solid #eee;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            background: rgba(52, 152, 219, 0.1);
            color: var(--accent-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
        }
        
        .activity-details {
            flex: 1;
        }
        
        .activity-details h4 {
            margin: 0 0 0.25rem;
            font-size: 1rem;
        }
        
        .activity-details p {
            margin: 0 0 0.25rem;
            color: #555;
        }
        
        .activity-time {
            font-size: 0.8rem;
            color: #777;
        }
        
        /* Workout section styles */
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .new-workout-btn {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.25rem;
            background: var(--secondary-color);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background var(--transition-speed) ease;
        }
        
        .new-workout-btn:hover {
            background: #c0392b;
        }
        
        .new-workout-btn i {
            margin-right: 0.5rem;
        }
        
        .workout-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        
        .workout-card {
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--card-shadow);
            transition: transform var(--transition-speed) ease;
        }
        
        .workout-card:hover {
            transform: translateY(-5px);
        }
        
        .workout-image {
            width: 100%;
            height: 160px;
            object-fit: cover;
        }
        
        .workout-content {
            padding: 1.5rem;
        }
        
        .workout-content h3 {
            margin: 0 0 0.75rem;
        }
        
        .workout-content p {
            margin: 0 0 1rem;
            color: #555;
        }
        
        .workout-meta {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1.25rem;
            color: #777;
        }
        
        .start-workout-btn {
            width: 100%;
            padding: 0.75rem;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background var(--transition-speed) ease;
        }
        
        .start-workout-btn:hover {
            background: #303f9f;
        }
        
        /* Modal styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            border-radius: var(--border-radius);
            width: 90%;
            max-width: 600px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem;
            border-bottom: 1px solid #eee;
        }
        
        .modal-header h2 {
            margin: 0;
        }
        
        .close-modal {
            font-size: 1.5rem;
            cursor: pointer;
            color: #777;
        }
        
        .modal-body {
            padding: 1.5rem;
        }
        
        .class-options {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .class-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 1rem;
            border: 2px solid #eee;
            border-radius: 8px;
            cursor: pointer;
            transition: all var(--transition-speed) ease;
        }
        
        .class-option.active {
            border-color: var(--accent-color);
            background: rgba(52, 152, 219, 0.05);
        }
        
        .class-option:hover {
            border-color: #ddd;
        }
        
        .class-option-img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 0.75rem;
        }
        
        .form-group {
            margin-bottom: 1.25rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
            padding: 1.5rem;
            border-top: 1px solid #eee;
        }
        
        .cancel-btn {
            padding: 0.75rem 1.5rem;
            background: #f5f5f5;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .confirm-btn {
            padding: 0.75rem 1.5rem;
            background: var(--accent-color);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        /* Responsive styles */
        @media (max-width: 992px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.active {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .sidebar-toggle {
                display: block;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 768px) {
            .quick-stats {
                grid-template-columns: 1fr;
            }
            
            .quick-actions {
                grid-template-columns: 1fr;
            }
            
            .schedule-item {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .class-image {
                margin-bottom: 1rem;
            }
            
            .schedule-time {
                margin-bottom: 0.5rem;
            }
            
            .schedule-status {
                margin-top: 0.5rem;
                align-self: flex-start;
            }
        }
    </style>
</head>
<body class="dashboard-body">
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="images/cuk-full-logo.png" alt="UniGym Logo" class="dashboard-logo">
                <h2>UniGym</h2>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li class="active">
                        <a href="#overview"><i class="fas fa-home"></i> Overview</a>
                    </li>
                    <li>
                        <a href="#workouts"><i class="fas fa-dumbbell"></i> Workouts</a>
                    </li>
                    <li>
                        <a href="#attendance"><i class="fas fa-calendar-check"></i> Attendance</a>
                    </li>
                    <li>
                        <a href="#schedule"><i class="fas fa-calendar"></i> Class Schedule</a>
                    </li>
                    <li>
                        <a href="#profile"><i class="fas fa-user"></i> Profile</a>
                    </li>
                    <li>
                        <a href="#notifications"><i class="fas fa-bell"></i> Notifications</a>
                    </li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <button id="logoutBtn" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </button>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="dashboard-header">
                <div class="header-left">
                    <button id="sidebarToggle" class="sidebar-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1>Dashboard</h1>
                </div>
                <div class="header-right">
                    <div class="notifications-dropdown">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                        <div class="dropdown-content">
                            <div class="notification-item">
                                <div class="notification-icon"><i class="fas fa-info-circle"></i></div>
                                <div class="notification-text">
                                    <p>New class schedule available</p>
                                    <span class="notification-time">2 hours ago</span>
                                </div>
                            </div>
                            <div class="notification-item">
                                <div class="notification-icon"><i class="fas fa-check-circle"></i></div>
                                <div class="notification-text">
                                    <p>Your booking was confirmed</p>
                                    <span class="notification-time">Yesterday</span>
                                </div>
                            </div>
                            <div class="notification-item">
                                <div class="notification-icon"><i class="fas fa-exclamation-circle"></i></div>
                                <div class="notification-text">
                                    <p>Maintenance scheduled for tomorrow</p>
                                    <span class="notification-time">2 days ago</span>
                                </div>
                            </div>
                            <a href="#notifications" class="view-all">View All Notifications</a>
                        </div>
                    </div>
                    <div class="user-info">
                        <span id="userFullName">John Doe</span>
                        <img src="images/default-avatar.png" alt="Profile" class="user-avatar">
                        <div class="user-dropdown">
                            <a href="#profile"><i class="fas fa-user"></i> My Profile</a>
                            <a href="#settings"><i class="fas fa-cog"></i> Settings</a>
                            <a href="#" id="logoutBtnMobile"><i class="fas fa-sign-out-alt"></i> Logout</a>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Dashboard Content -->
            <div class="dashboard-content">
                <!-- Overview Section -->
                <section id="overview" class="dashboard-section active">
                    <div class="welcome-banner">
                        <h2>Welcome back, <span id="userName">John</span>!</h2>
                        <p class="current-time" id="currentTime"></p>
                    </div>

                    <div class="quick-stats">
                        <div class="stat-card">
                            <i class="fas fa-clock"></i>
                            <div class="stat-info">
                                <h3>Today's Workout</h3>
                                <p id="todayWorkout">2h 15m</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <i class="fas fa-fire"></i>
                            <div class="stat-info">
                                <h3>Calories Burned</h3>
                                <p id="caloriesBurned">450 kcal</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <i class="fas fa-trophy"></i>
                            <div class="stat-info">
                                <h3>Workout Streak</h3>
                                <p id="workoutStreak">5 days</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <i class="fas fa-heartbeat"></i>
                            <div class="stat-info">
                                <h3>Fitness Score</h3>
                                <p id="fitnessScore">78/100</p>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-grid">
                        <!-- Workout Progress Chart -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Workout Progress</h3>
                                <div class="card-actions">
                                    <button class="card-action-btn" id="weeklyViewBtn">Weekly</button>
                                    <button class="card-action-btn" id="monthlyViewBtn">Monthly</button>
                                </div>
                            </div>
                            <canvas id="workoutChart"></canvas>
                        </div>

                        <!-- Today's Schedule -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Today's Schedule</h3>
                                <a href="#schedule" class="view-all-link">View All</a>
                            </div>
                            <div class="schedule-list" id="todaySchedule">
                                <div class="schedule-item">
                                    <img src="images/classes/yoga.jpg" alt="Yoga" class="class-image">
                                    <div class="schedule-time">10:00 AM</div>
                                    <div class="schedule-details">
                                        <h4>Yoga Class</h4>
                                        <div class="instructor-info">
                                            <img src="images/instructors/sarah.jpg" alt="Sarah" class="instructor-avatar">
                                            <p>Instructor: Sarah Johnson</p>
                                        </div>
                                        <p>Studio B</p>
                                    </div>
                                    <div class="schedule-status">Booked</div>
                                </div>
                                <div class="schedule-item">
                                    <img src="images/classes/personal-training.jpg" alt="Personal Training" class="class-image">
                                    <div class="schedule-time">2:30 PM</div>
                                    <div class="schedule-details">
                                        <h4>Personal Training</h4>
                                        <div class="instructor-info">
                                            <img src="images/instructors/mike.jpg" alt="Mike" class="instructor-avatar">
                                            <p>Trainer: Mike Thompson</p>
                                        </div>
                                        <p>Training Area</p>
                                    </div>
                                    <div class="schedule-status">Confirmed</div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="dashboard-card">
                            <h3>Quick Actions</h3>
                            <div class="quick-actions">
                                <button id="checkInBtn" class="action-btn">
                                    <i class="fas fa-sign-in-alt"></i> Check In
                                </button>
                                <button id="bookClassBtn" class="action-btn">
                                    <i class="fas fa-plus"></i> Book Class
                                </button>
                                <button id="startWorkoutBtn" class="action-btn">
                                    <i class="fas fa-play"></i> Start Workout
                                </button>
                                <button id="scanQRBtn" class="action-btn">
                                    <i class="fas fa-qrcode"></i> Scan QR Code
                                </button>
                            </div>
                        </div>

                        <!-- Recent Activities -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Recent Activities</h3>
                                <a href="#activities" class="view-all-link">View All</a>
                            </div>
                            <div class="activity-feed" id="recentActivities">
                                <div class="activity-item">
                                    <div class="activity-icon"><i class="fas fa-dumbbell"></i></div>
                                    <div class="activity-details">
                                        <h4>Completed Workout</h4>
                                        <p>Upper Body Strength - 45 minutes</p>
                                        <span class="activity-time">Today, 9:30 AM</span>
                                    </div>
                                </div>
                                <div class="activity-item">
                                    <div class="activity-icon"><i class="fas fa-calendar-check"></i></div>
                                    <div class="activity-details">
                                        <h4>Class Booked</h4>
                                        <p>Yoga Class - Tomorrow at 10:00 AM</p>
                                        <span class="activity-time">Today, 8:15 AM</span>
                                    </div>
                                </div>
                                <div class="activity-item">
                                    <div class="activity-icon"><i class="fas fa-running"></i></div>
                                    <div class="activity-details">
                                        <h4>Completed Workout</h4>
                                        <p>Cardio Session - 30 minutes</p>
                                        <span class="activity-time">Yesterday, 5:45 PM</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Workouts Section -->
                <section id="workouts" class="dashboard-section">
                    <div class="section-header">
                        <h2>My Workouts</h2>
                        <button class="new-workout-btn">
                            <i class="fas fa-plus"></i> New Workout
                        </button>
                    </div>
                    <div class="workout-grid">
                        <!-- Example workout cards with images -->
                        <div class="workout-card">
                            <img src="images/workouts/strength.jpg" alt="Strength Training" class="workout-image">
                            <div class="workout-content">
                                <h3>Strength Training</h3>
                                <p>Full body workout focusing on major muscle groups</p>
                                <div class="workout-meta">
                                    <span><i class="fas fa-clock"></i> 45 min</span>
                                    <span><i class="fas fa-fire"></i> 350 kcal</span>
                                </div>
                                <button class="start-workout-btn">Start Workout</button>
                            </div>
                        </div>
                        
                        <div class="workout-card">
                            <img src="images/workouts/cardio.jpg" alt="Cardio" class="workout-image">
                            <div class="workout-content">
                                <h3>Cardio Blast</h3>
                                <p>High-intensity interval training for maximum calorie burn</p>
                                <div class="workout-meta">
                                    <span><i class="fas fa-clock"></i> 30 min</span>
                                    <span><i class="fas fa-fire"></i> 400 kcal</span>
                                </div>
                                <button class="start-workout-btn">Start Workout</button>
                            </div>
                        </div>
                        
                        <div class="workout-card">
                            <img src="images/workouts/yoga.jpg" alt="Yoga" class="workout-image">
                            <div class="workout-content">
                                <h3>Yoga Flow</h3>
                                <p>Improve flexibility and mindfulness with this yoga routine</p>
                                <div class="workout-meta">
                                    <span><i class="fas fa-clock"></i> 60 min</span>
                                    <span><i class="fas fa-fire"></i> 200 kcal</span>
                                </div>
                                <button class="start-workout-btn">Start Workout</button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Attendance Section -->
                <section id="attendance" class="dashboard-section">
                    <div class="attendance-container">
                        <h2>Attendance History</h2>
                        <div class="attendance-filters">
                            <select id="monthFilter">
                                <option value="">Select Month</option>
                            </select>
                            <select id="yearFilter">
                                <option value="">Select Year</option>
                            </select>
                        </div>
                        <div class="attendance-calendar" id="attendanceCalendar">
                            <!-- Calendar will be populated by JavaScript -->
                        </div>
                        <div class="attendance-stats">
                            <div class="stat-item">
                                <h4>Total Hours</h4>
                                <p id="totalHours">45h</p>
                            </div>
                            <div class="stat-item">
                                <h4>Average/Day</h4>
                                <p id="averageHours">1.5h</p>
                            </div>
                            <div class="stat-item">
                                <h4>Best Streak</h4>
                                <p id="bestStreak">7 days</p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Profile Section -->
                <section id="profile" class="dashboard-section">
                    <div class="profile-container">
                        <div class="profile-header">
                            <div class="profile-avatar-container">
                                <img src="images/default-avatar.png" alt="Profile" id="profileImage">
                                <button class="change-avatar-btn">
                                    <i class="fas fa-camera"></i>
                                </button>
                            </div>
                            <div class="profile-info">
                                <h2 id="profileName">John Doe</h2>
                                <p id="membershipStatus">Premium Member</p>
                            </div>
                        </div>
                        <div class="profile-details">
                            <form id="profileForm">
                                <div class="form-group">
                                    <label>Student ID</label>
                                    <input type="text" id="studentId" readonly>
                                </div>
                                <div class="form-group">
                                    <label>Email</label>
                                    <input type="email" id="email">
                                </div>
                                <div class="form-group">
                                    <label>Phone</label>
                                    <input type="tel" id="phone">
                                </div>
                                <div class="form-group">
                                    <label>Department</label>
                                    <input type="text" id="department">
                                </div>
                                <button type="submit" class="save-profile-btn">
                                    Save Changes
                                </button>
                            </form>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <!-- New Modal Components -->
    <div id="bookClassModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Book a Class</h2>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="class-options">
                    <div class="class-option" data-value="yoga">
                        <img src="images/classes/yoga.jpg" alt="Yoga" class="class-option-img">
                        <span>Yoga</span>
                    </div>
                    <div class="class-option" data-value="pilates">
                        <img src="images/classes/pilates.jpg" alt="Pilates" class="class-option-img">
                        <span>Pilates</span>
                    </div>
                    <div class="class-option" data-value="zumba">
                        <img src="images/classes/zumba.jpg" alt="Zumba" class="class-option-img">
                        <span>Zumba</span>
                    </div>
                    <div class="class-option" data-value="spinning">
                        <img src="images/classes/spinning.jpg" alt="Spinning" class="class-option-img">
                        <span>Spinning</span>
                    </div>
                    <div class="class-option" data-value="hiit">
                        <img src="images/classes/hiit.jpg" alt="HIIT" class="class-option-img">
                        <span>HIIT</span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="classDate">Date</label>
                    <input type="date" id="classDate">
                </div>
                <div class="form-group">
                    <label for="classTime">Time</label>
                    <select id="classTime">
                        <option value="">Select a time</option>
                        <!-- Will be populated based on selected date and class -->
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button class="cancel-btn">Cancel</button>
                <button class="confirm-btn">Book Class</button>
            </div>
        </div>
    </div>

    <script src="js/dashboard.js"></script>
    <script>
        // Initialize dropdown functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Notifications dropdown
            const notificationsDropdown = document.querySelector('.notifications-dropdown');
            notificationsDropdown.addEventListener('click', function(e) {
                this.classList.toggle('active');
                e.stopPropagation();
            });
            
            // User dropdown
            const userInfo = document.querySelector('.user-info');
            userInfo.addEventListener('click', function(e) {
                this.classList.toggle('active');
                e.stopPropagation();
            });
            
            // Close dropdowns when clicking elsewhere
            document.addEventListener('click', function() {
                notificationsDropdown.classList.remove('active');
                userInfo.classList.remove('active');
            });
            
            // Mobile logout button
            document.getElementById('logoutBtnMobile').addEventListener('click', function() {
                // Call the same function as the main logout button
                document.getElementById('logoutBtn').click();
            });
            
            // Book class button
            document.getElementById('bookClassBtn').addEventListener('click', function() {
                document.getElementById('bookClassModal').style.display = 'flex';
            });
            
            // Close modal
            document.querySelector('.close-modal').addEventListener('click', function() {
                document.getElementById('bookClassModal').style.display = 'none';
            });
            
            document.querySelector('.cancel-btn').addEventListener('click', function() {
                document.getElementById('bookClassModal').style.display = 'none';
            });
            
            // Class option selection
            const classOptions = document.querySelectorAll('.class-option');
            classOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // Remove active class from all options
                    classOptions.forEach(opt => opt.classList.remove('active'));
                    // Add active class to clicked option
                    this.classList.add('active');
                });
            });
            
            // Display current time
            function updateTime() {
                const now = new Date();
                const options = { 
                    weekday: 'long', 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                };
                document.getElementById('currentTime').textContent = now.toLocaleDateString('en-US', options);
            }
            
            updateTime();
            setInterval(updateTime, 60000); // Update every minute
        });
    </script>
</body>
</html>














