<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - University Gym</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="dashboard-body">
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="images/logos/cuk-full-logo.png" alt="UniGym Logo" class="dashboard-logo">
                <h2>UniGym</h2>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li class="active">
                        <a href="#overview"><i class="fas fa-home"></i> Overview</a>
                    </li>
                    <li>
                        <a href="workouts.php"><i class="fas fa-dumbbell"></i> Workouts</a>
                    </li>
                    <li>
                        <a href="attendance.php"><i class="fas fa-calendar-check"></i> Attendance</a>
                    </li>
                    <li>
                        <a href="schedule.php"><i class="fas fa-calendar"></i> Class Schedule</a>
                    </li>
                    <li>
                        <a href="profile.php"><i class="fas fa-user"></i> Profile</a>
                    </li>
                    <li>
                        <a href="notifications.php"><i class="fas fa-bell"></i> Notifications</a>
                    </li>
                    <li>
                        <a href="facilities.php"><i class="fas fa-building"></i> Facilities</a>
                    </li>
                    <li>
                        <a href="reports.php"><i class="fas fa-chart-bar"></i> Reports</a>
                    </li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <a href="logout.php" id="logoutBtn" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="dashboard-header">
                <div class="header-left">
                    <button id="sidebarToggle" class="sidebar-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1>Dashboard</h1>
                </div>
                <div class="header-right">
                    <div class="notifications-dropdown">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                        <div class="dropdown-content">
                            <div class="notification-item">
                                <div class="notification-icon"><i class="fas fa-info-circle"></i></div>
                                <div class="notification-text">
                                    <p>New class schedule available</p>
                                    <span class="notification-time">2 hours ago</span>
                                </div>
                            </div>
                            <div class="notification-item">
                                <div class="notification-icon"><i class="fas fa-check-circle"></i></div>
                                <div class="notification-text">
                                    <p>Your booking was confirmed</p>
                                    <span class="notification-time">Yesterday</span>
                                </div>
                            </div>
                            <div class="notification-item">
                                <div class="notification-icon"><i class="fas fa-exclamation-circle"></i></div>
                                <div class="notification-text">
                                    <p>Maintenance scheduled for tomorrow</p>
                                    <span class="notification-time">2 days ago</span>
                                </div>
                            </div>
                            <a href="notifications.php" class="view-all">View All Notifications</a>
                        </div>
                    </div>
                    <div class="user-info">
                        <span id="userFullName"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></span>
                        <img src="images/users/default-avatar.png" alt="Profile" class="user-avatar">
                        <div class="user-dropdown">
                            <a href="profile.php"><i class="fas fa-user"></i> My Profile</a>
                            <a href="settings.php"><i class="fas fa-cog"></i> Settings</a>
                            <a href="logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Dashboard Content -->
            <div class="dashboard-content">
                <!-- Overview Section -->
                <section id="overview" class="dashboard-section active">
                    <div class="welcome-banner">
                        <h2>Welcome back, <span id="userName"><?php echo htmlspecialchars($user['first_name']); ?></span>!</h2>
                        <p class="current-time" id="currentTime"></p>
                    </div>

                    <!-- Dashboard content continues as in the HTML version -->
                    <!-- ... -->
                </section>
            </div>
        </main>
    </div>

    <script src="js/dashboard.js"></script>
</body>
</html>