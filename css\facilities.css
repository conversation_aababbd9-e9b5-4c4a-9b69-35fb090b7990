/* Facilities Hero Section */
.facilities-hero {
    background: linear-gradient(rgba(26, 35, 126, 0.7), rgba(0, 0, 0, 0.8)),
                url('../images/hero/gym-hero-background.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed; /* Creates parallax effect */
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: var(--white);
    margin-top: 60px;
    position: relative;
    overflow: hidden;
}

.facilities-hero:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, transparent 0%, rgba(0,0,0,0.4) 100%);
    z-index: 1;
}

.facilities-hero .hero-content {
    max-width: 800px;
    padding: 0 20px;
    position: relative;
    z-index: 2;
    animation: fadeInUp 1.2s ease;
}

.facilities-hero h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 2px;
    text-shadow: 0 2px 10px rgba(0,0,0,0.3);
    font-weight: 800;
    animation: float 6s ease-in-out infinite;
}

.facilities-hero p {
    font-size: 1.2rem;
    opacity: 0.9;
    text-shadow: 0 2px 5px rgba(0,0,0,0.3);
    animation: float 6s ease-in-out 0.5s infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .facilities-hero {
        background-attachment: scroll; /* Better performance on mobile */
        height: 350px;
    }
    
    .facilities-hero h1 {
        font-size: 2.8rem;
    }
    
    .facilities-hero p {
        font-size: 1.1rem;
    }
}

/* Facilities Section */
.facilities-section {
    padding: 5rem 5%;
    background-color: var(--light-gray);
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-header h2 {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.section-header p {
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

.facilities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    padding: 1rem;
}

/* Facility Cards */
.facility-card {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.facility-card:hover {
    transform: translateY(-10px);
}

.facility-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.facility-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.facility-card:hover .facility-image img {
    transform: scale(1.1);
}

.facility-overlay {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(0,0,0,0.7);
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

.availability {
    color: var(--white);
    font-size: 0.8rem;
    font-weight: 500;
}

.facility-content {
    padding: 2rem;
}

.facility-icon {
    width: 50px;
    height: 50px;
    background: var(--secondary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.facility-icon i {
    color: var(--white);
    font-size: 1.5rem;
}

.facility-content h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.facility-content p {
    color: #666;
    margin-bottom: 1.5rem;
}

.facility-features {
    list-style: none;
    padding: 0;
}

.facility-features li {
    margin-bottom: 0.5rem;
    color: #555;
}

.facility-features i {
    color: var(--secondary-color);
    margin-right: 0.5rem;
}

/* CTA Section */
.facility-cta {
    background: linear-gradient(rgba(0,0,0,0.8), rgba(0,0,0,0.8)),
                url('../images/gym-cta-bg.jpg');
    background-size: cover;
    background-position: center;
    padding: 5rem 2rem;
    text-align: center;
    color: var(--white);
}

.cta-content {
    max-width: 800px;
    margin: 0 auto;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

.btn-primary, .btn-secondary {
    padding: 1rem 2rem;
    border-radius: 30px;
    font-weight: 600;
    text-decoration: none;
    transition: transform 0.3s ease;
}

.btn-primary {
    background: var(--secondary-color);
    color: var(--white);
}

.btn-secondary {
    background: transparent;
    border: 2px solid var(--white);
    color: var(--white);
}

.btn-primary:hover, .btn-secondary:hover {
    transform: translateY(-3px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .facilities-hero h1 {
        font-size: 2.5rem;
    }

    .facilities-grid {
        grid-template-columns: 1fr;
    }

    .cta-buttons {
        flex-direction: column;
    }

    .btn-primary, .btn-secondary {
        width: 100%;
        text-align: center;
    }
}

/* Animation Classes */
[data-aos] {
    opacity: 0;
    transition-property: opacity, transform;
}

[data-aos].aos-animate {
    opacity: 1;
}

/* Animation keyframes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}




