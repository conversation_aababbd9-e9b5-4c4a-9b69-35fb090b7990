const express = require('express');
const router = express.Router();
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const pool = require('../config/database');

// Validation helper functions
function validateEmail(email) {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
}

function validatePhone(phone) {
    return /^\d{10}$/.test(phone);
}

function validateStudentId(studentId) {
    return /^[A-Za-z0-9]{6,20}$/.test(studentId);
}

// Login endpoint
router.post('/login', async (req, res) => {
    try {
        const { student_id, password } = req.body;

        // Basic validation
        if (!student_id || !password) {
            return res.status(400).json({
                success: false,
                message: 'Student ID and password are required'
            });
        }

        // Log the received credentials (remove in production)
        console.log('Login attempt:', { student_id, passwordLength: password?.length });

        const [users] = await pool.execute(
            'SELECT * FROM students WHERE student_id = ?',
            [student_id]
        );

        if (!users.length) {
            return res.status(401).json({
                success: false,
                message: 'Invalid credentials'
            });
        }

        const user = users[0];
        const validPassword = await bcrypt.compare(password, user.password);

        if (!validPassword) {
            return res.status(401).json({
                success: false,
                message: 'Invalid credentials'
            });
        }

        const token = jwt.sign(
            {
                id: user.id,
                role: user.role || 'student',
                student_id: user.student_id
            },
            process.env.JWT_SECRET,
            { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
        );

        // Set HTTP-only cookie
        res.cookie('token', token, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            maxAge: 24 * 60 * 60 * 1000 // 24 hours
        });

        // Always return a JSON response
        return res.json({
            success: true,
            message: 'Login successful',
            token: token,
            user: {
                id: user.id,
                student_id: user.student_id,
                full_name: user.full_name,
                email: user.email,
                role: user.role || 'student'
            }
        });

    } catch (error) {
        console.error('Login error:', error);
        return res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

// Logout endpoint
router.post('/logout', (req, res) => {
    res.clearCookie('token');
    res.json({
        success: true,
        message: 'Logged out successfully'
    });
});

// Registration endpoint
router.post('/register', async (req, res) => {
    try {
        // Log the received data
        console.log('Registration attempt:', req.body);
        
        // Extract user data
        const { 
            studentId, 
            fullName, 
            email, 
            phone, 
            gender,
            department,
            course,
            year,
            weight,
            height,
            medical,
            password
        } = req.body;
        
        // Validate required fields
        if (!studentId || !fullName || !email || !phone || !gender || 
            !department || !course || !year || !password) {
            return res.status(400).json({ 
                success: false,
                message: 'Please fill in all required fields' 
            });
        }
        
        // Check if student ID already exists
        const [existingUsers] = await pool.execute(
            'SELECT * FROM students WHERE student_id = ?',
            [studentId]
        );
        
        if (existingUsers.length > 0) {
            return res.status(409).json({ 
                success: false,
                message: 'Student ID already registered' 
            });
        }
        
        // Check if email already exists
        const [existingEmails] = await pool.execute(
            'SELECT * FROM students WHERE email = ?',
            [email]
        );
        
        if (existingEmails.length > 0) {
            return res.status(409).json({ 
                success: false,
                message: 'Email already registered' 
            });
        }
        
        // Hash the password
        const hashedPassword = await bcrypt.hash(password, 10);
        
        // Insert new user
        await pool.execute(
            `INSERT INTO students (
                student_id, full_name, email, phone, gender, 
                department, course, year, weight, height,
                medical_conditions, password, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())`,
            [
                studentId, fullName, email, phone, gender,
                department, course, year, 
                weight || null, height || null,
                medical === 'yes' ? req.body['medical-description'] : null,
                hashedPassword
            ]
        );
        
        // Return success response
        res.status(201).json({ 
            success: true,
            message: 'Registration successful' 
        });
        
    } catch (error) {
        console.error('Registration error:', error);
        res.status(500).json({ 
            success: false,
            message: 'Internal server error',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

module.exports = router;





