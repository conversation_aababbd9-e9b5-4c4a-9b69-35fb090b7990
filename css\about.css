/* About Page Specific Styles */

/* Hero Section */
.about-hero {
    background: linear-gradient(rgba(26, 35, 126, 0.7), rgba(0, 0, 0, 0.8)),
                url('../images/hero/gym-hero-background.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed; /* Creates parallax effect */
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: var(--white);
    margin-top: 60px;
    position: relative;
    overflow: hidden;
}

.about-hero:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, transparent 0%, rgba(0,0,0,0.4) 100%);
    z-index: 1;
}

.about-hero .hero-content {
    max-width: 800px;
    padding: 0 20px;
    position: relative;
    z-index: 2;
    animation: fadeInUp 1.2s ease;
}

.about-hero h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 2px;
    text-shadow: 0 2px 10px rgba(0,0,0,0.3);
    font-weight: 800;
    animation: float 6s ease-in-out infinite;
}

.about-hero p {
    font-size: 1.2rem;
    opacity: 0.9;
    text-shadow: 0 2px 5px rgba(0,0,0,0.3);
    animation: float 6s ease-in-out 0.5s infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .about-hero {
        background-attachment: scroll; /* Better performance on mobile */
        height: 350px;
    }
    
    .about-hero h1 {
        font-size: 2.8rem;
    }
    
    .about-hero p {
        font-size: 1.1rem;
    }
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Section Title */
.section-title {
    font-size: 2.5rem;
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 2.5rem;
    position: relative;
    padding-bottom: 15px;
}

.section-title:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background-color: var(--secondary-color);
}

/* About Content Section */
.about-content-section {
    padding: 5rem 0;
    background-color: var(--white);
}

.about-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
}

.about-text {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.about-card {
    padding: 30px;
    border-radius: 10px;
    box-shadow: var(--card-shadow);
    transition: transform 0.3s ease;
    background-color: var(--light-bg);
}

.about-card:hover {
    transform: translateY(-5px);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 20px;
}

.about-card h2 {
    font-size: 1.8rem;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.about-card p {
    color: #555;
    line-height: 1.7;
}

/* Stats Container */
.stats-container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 20px;
}

.stat-item {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    box-shadow: var(--card-shadow);
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.stat-item p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Values Section */
.values-section {
    padding: 5rem 0;
    background-color: var(--light-bg);
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.value-card {
    background-color: var(--white);
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    box-shadow: var(--card-shadow);
    transition: transform 0.3s ease;
}

.value-card:hover {
    transform: translateY(-5px);
}

.value-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background-color: var(--secondary-color);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    margin: 0 auto 20px;
}

.value-card h3 {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.value-card p {
    color: #555;
}

/* Team Section */
.team-section {
    padding: 5rem 0;
    background-color: var(--white);
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.team-member {
    background-color: var(--light-bg);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--card-shadow);
    transition: transform 0.3s ease;
}

.team-member:hover {
    transform: translateY(-5px);
}

.member-image {
    height: 250px;
    overflow: hidden;
}

.member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.team-member:hover .member-image img {
    transform: scale(1.05);
}

.member-info {
    padding: 20px;
}

.member-info h3 {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.member-title {
    color: var(--secondary-color);
    font-weight: 600;
    margin-bottom: 15px;
}

.member-bio {
    color: #555;
    line-height: 1.6;
}

/* Milestones Section */
.milestones-section {
    padding: 5rem 0;
    background-color: var(--white);
}

.milestones-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.milestone-card {
    background-color: var(--light-bg);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--card-shadow);
    transition: transform 0.3s ease;
}

.milestone-card:hover {
    transform: translateY(-5px);
}

.milestone-image {
    height: 250px;
    overflow: hidden;
}

.milestone-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.milestone-card:hover .milestone-image img {
    transform: scale(1.05);
}

.milestone-info {
    padding: 20px;
}

.milestone-info h3 {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.milestone-title {
    color: var(--secondary-color);
    font-weight: 600;
    margin-bottom: 15px;
}

.milestone-bio {
    color: #555;
    line-height: 1.6;
}

/* Animation keyframes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}


