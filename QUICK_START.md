# 🚀 <PERSON><PERSON><PERSON><PERSON> START GUIDE - Run Your Project in 5 Minutes

## 📋 Prerequisites Check

Before running your project, you need:
- ✅ Node.js (JavaScript runtime)
- ✅ MySQL (Database)

## 🔧 Step-by-Step Setup

### **Step 1: Install Node.js**
1. Go to: **https://nodejs.org/**
2. Click **"Download for Windows"** (LTS version)
3. Run the downloaded file
4. Click **"Next"** through all steps
5. **Restart your computer**

### **Step 2: Install MySQL**
1. Go to: **https://dev.mysql.com/downloads/mysql/**
2. Click **"Download"** for MySQL Community Server
3. Run the installer
4. Choose **"Developer Default"** setup
5. **Remember your root password!**
6. Complete installation

### **Step 3: Configure Project**
1. Open your project folder
2. Find the **`.env`** file
3. Edit line 9: `DB_PASSWORD=your_actual_mysql_password`
4. Save the file

### **Step 4: Run Project**
1. **Right-click** in your project folder
2. Select **"Open in Terminal"** or **"Open Command Prompt here"**
3. Type these commands **one by one**:

```bash
npm install
```
*(Wait for it to finish)*

```bash
npm run init-db
```
*(Wait for database setup)*

```bash
npm run dev
```
*(Server will start)*

### **Step 5: Open Your Application**
1. Open your web browser
2. Go to: **http://localhost:3000**
3. For admin panel: **http://localhost:3000/admin/dashboard.html**

## 🔐 Login Information

**Admin Login:**
- Username: `admin`
- Password: `admin123`

**Student Registration:**
- Click "Register" on the main page
- Fill out the form to create student accounts

## 🐛 Troubleshooting

### **"node is not recognized"**
- Node.js not installed or computer not restarted
- Install Node.js and restart computer

### **"npm install failed"**
- Run: `npm install --force`
- Or delete `node_modules` folder and try again

### **"Database connection failed"**
- Check MySQL is running
- Verify password in `.env` file
- Try: `mysql -u root -p` in command prompt

### **"Port 3000 already in use"**
- Close other applications using port 3000
- Or change PORT in `.env` file to 3001

### **"Cannot find module"**
- Delete `node_modules` folder
- Run `npm install` again

## 🎯 What You'll See

### **Main Website (http://localhost:3000)**
- University gym homepage
- Student registration form
- Login page
- About and contact pages

### **Admin Panel (http://localhost:3000/admin/dashboard.html)**
- Dashboard with statistics
- Student management
- Attendance tracking
- Class management
- Reports and analytics

## 📱 Features You Can Demo

1. **Student Registration** - Complete multi-step form
2. **Student Login** - Secure authentication
3. **Admin Dashboard** - Management interface
4. **Database Integration** - All data stored in MySQL
5. **Responsive Design** - Works on mobile and desktop

## 🔄 Starting/Stopping the Server

### **To Start:**
```bash
npm run dev
```

### **To Stop:**
- Press `Ctrl + C` in the terminal
- Or close the command prompt window

### **To Restart:**
- Stop the server (Ctrl + C)
- Run `npm run dev` again

## 📞 Need Help?

If something doesn't work:
1. Check this guide again
2. Make sure MySQL is running
3. Verify `.env` file has correct password
4. Try restarting your computer
5. Check for error messages in the terminal

## 🎉 Success Indicators

You'll know it's working when you see:
- ✅ "Server is running on port 3000"
- ✅ "Database connected successfully"
- ✅ Website loads at http://localhost:3000
- ✅ Admin panel accessible
- ✅ Can register new students

Your University Gym Management System is now ready to use!
