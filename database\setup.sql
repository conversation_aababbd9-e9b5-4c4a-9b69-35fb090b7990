-- Create database if it doesn't exist
CREATE DATABASE IF NOT EXISTS gym_management;

-- Use the database
USE gym_management;

-- Create students table
CREATE TABLE IF NOT EXISTS students (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id VARCHAR(20) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(15) NOT NULL,
    gender ENUM('male', 'female') NOT NULL,
    department VARCHAR(50) NOT NULL,
    course VARCHAR(50) NOT NULL,
    year VARCHAR(10) NOT NULL,
    weight DECIMAL(5,2),
    height DECIMAL(5,2),
    medical_conditions TEXT,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'student',
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create admins table
CREATE TABLE IF NOT EXISTS admins (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'admin',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create attendance table
CREATE TABLE IF NOT EXISTS attendance (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    check_in DATETIME NOT NULL,
    check_out DATETIME,
    date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE
);

-- Create classes table
CREATE TABLE IF NOT EXISTS classes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    class_type VARCHAR(50) NOT NULL,
    instructor VARCHAR(100) NOT NULL,
    capacity INT NOT NULL DEFAULT 20,
    duration INT NOT NULL DEFAULT 60,
    start_time DATETIME NOT NULL,
    end_time DATETIME NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create bookings table
CREATE TABLE IF NOT EXISTS bookings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    class_id INT NOT NULL,
    status ENUM('confirmed', 'waitlist', 'cancelled') DEFAULT 'confirmed',
    booking_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE
);

-- Create facilities table
CREATE TABLE IF NOT EXISTS facilities (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    capacity INT NOT NULL,
    status ENUM('available', 'maintenance', 'closed') DEFAULT 'available',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default admin user (password: admin123)
INSERT IGNORE INTO admins (username, email, full_name, password, role) VALUES 
('admin', '<EMAIL>', 'System Administrator', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq9w5KS', 'admin');

-- Insert sample facilities
INSERT IGNORE INTO facilities (name, description, capacity, status) VALUES 
('Main Gym Floor', 'Main workout area with cardio and strength equipment', 50, 'available'),
('Weight Training Area', 'Dedicated area for weight training and powerlifting', 30, 'available'),
('Yoga Studio', 'Peaceful studio for yoga and meditation classes', 25, 'available'),
('Swimming Pool', 'Olympic-size swimming pool for lap swimming', 40, 'available'),
('Basketball Court', 'Full-size basketball court for games and practice', 20, 'available');

-- Insert sample classes
INSERT IGNORE INTO classes (name, description, class_type, instructor, capacity, duration, start_time, end_time, status) VALUES 
('Morning Yoga', 'Relaxing yoga session to start your day', 'Yoga', 'Sarah Johnson', 20, 60, '2024-01-15 07:00:00', '2024-01-15 08:00:00', 'active'),
('HIIT Training', 'High-intensity interval training for maximum results', 'Cardio', 'Mike Wilson', 15, 45, '2024-01-15 18:00:00', '2024-01-15 18:45:00', 'active'),
('Strength Training', 'Build muscle and increase strength', 'Strength', 'John Davis', 12, 90, '2024-01-15 19:00:00', '2024-01-15 20:30:00', 'active'),
('Aqua Aerobics', 'Low-impact water-based exercise', 'Aqua', 'Lisa Brown', 25, 60, '2024-01-15 16:00:00', '2024-01-15 17:00:00', 'active');

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_students_student_id ON students(student_id);
CREATE INDEX IF NOT EXISTS idx_students_email ON students(email);
CREATE INDEX IF NOT EXISTS idx_attendance_student_id ON attendance(student_id);
CREATE INDEX IF NOT EXISTS idx_attendance_date ON attendance(date);
CREATE INDEX IF NOT EXISTS idx_bookings_user_id ON bookings(user_id);
CREATE INDEX IF NOT EXISTS idx_bookings_class_id ON bookings(class_id);
CREATE INDEX IF NOT EXISTS idx_classes_start_time ON classes(start_time);
