@echo off
echo ========================================
echo   Setting Up on New Laptop
echo ========================================
echo.

echo 🔍 Checking prerequisites...
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js not found!
    echo 📥 Please install Node.js from: https://nodejs.org/
    echo.
    pause
    exit /b 1
) else (
    echo ✅ Node.js found
    node --version
)

REM Check if npm is available
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm not found!
    echo 📥 Please reinstall Node.js from: https://nodejs.org/
    echo.
    pause
    exit /b 1
) else (
    echo ✅ npm found
    npm --version
)

echo.
echo 🔧 Setting up project...
echo.

REM Install dependencies
echo 📦 Installing dependencies...
npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    echo 🔧 Try running: npm install --force
    pause
    exit /b 1
)
echo ✅ Dependencies installed

echo.
echo 🗄️  Setting up database...
echo.
echo ⚠️  Make sure MySQL is running and .env file has correct password!
echo.
pause

REM Setup database
npm run init-db
if %errorlevel% neq 0 (
    echo ❌ Database setup failed
    echo 🔧 Check:
    echo    1. MySQL is running
    echo    2. .env file has correct DB_PASSWORD
    echo    3. MySQL credentials are correct
    pause
    exit /b 1
)

echo.
echo ✅ Setup complete!
echo.
echo 🚀 Starting the application...
echo.
echo 🌐 Open your browser to: http://localhost:3000
echo 👤 Admin login: admin / admin123
echo.
echo 🔧 To stop the server, press Ctrl+C
echo.

npm run dev
