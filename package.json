{"name": "gym-management-system", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node scripts/init-database.js", "setup": "npm install && npm run init-db", "test": "echo \"No tests specified\" && exit 0"}, "dependencies": {"bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.0"}, "devDependencies": {"nodemon": "^3.1.9"}}