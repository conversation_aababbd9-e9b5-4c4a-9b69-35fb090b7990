.class-card {
    border: 1px solid #ddd;
    padding: 15px;
    margin: 10px 0;
    border-radius: 8px;
    background: #fff;
}

.class-card.full {
    background: #f5f5f5;
}

.class-details {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin: 10px 0;
}

.book-button {
    width: 100%;
    padding: 10px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.book-button[data-waitlist="true"] {
    background: #6c757d;
}

.booking-card {
    border: 1px solid #ddd;
    padding: 15px;
    margin: 10px 0;
    border-radius: 8px;
}

.booking-card.confirmed {
    border-left: 4px solid #28a745;
}

.booking-card.waitlist {
    border-left: 4px solid #ffc107;
}

.booking-card.cancelled {
    border-left: 4px solid #dc3545;
}

.booking-details {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-top: 10px;
}
