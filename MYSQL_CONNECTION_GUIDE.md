# 🔗 MySQL Connection Guide for Gym Management System

## 🎯 Overview

Your project uses MySQL database to store:
- Student information
- Admin accounts
- Attendance records
- Class schedules
- Facility data

## 📋 Connection Architecture

```
Your Project (Node.js) ←→ MySQL Database
     ↓                        ↓
config/database.js      gym_management database
     ↓                        ↓
.env file              Tables (students, admins, etc.)
```

## 🔧 Step-by-Step Connection Setup

### **Step 1: Install MySQL**

#### **Option A: MySQL Installer (Recommended)**
1. **Download**: https://dev.mysql.com/downloads/mysql/
2. **Choose**: MySQL Community Server
3. **Install**: Select "Developer Default"
4. **Set Password**: Remember your root password!
5. **Start Service**: MySQL should start automatically

#### **Option B: MySQL Workbench (Easier GUI)**
1. **Download**: https://dev.mysql.com/downloads/workbench/
2. **Install**: Follow the wizard
3. **Setup**: It will guide you through server installation

### **Step 2: Verify MySQL Installation**

Open Command Prompt and test:
```bash
mysql --version
```

If you see version info, MySQL is installed!

### **Step 3: Test MySQL Connection**

Try connecting manually:
```bash
mysql -u root -p
```
Enter your password when prompted.

### **Step 4: Configure Project Connection**

#### **Update .env File**
Edit your `.env` file with correct MySQL credentials:

```env
# Database Configuration
DB_HOST=localhost          # Usually localhost
DB_USER=root              # Usually root
DB_PASSWORD=your_password # Your MySQL root password
DB_NAME=gym_management    # Database name (auto-created)
DB_PORT=3306             # Default MySQL port
```

#### **Common Configurations:**

**No Password Setup:**
```env
DB_PASSWORD=
```

**Custom User:**
```env
DB_USER=gym_user
DB_PASSWORD=gym_password
```

**Different Port:**
```env
DB_PORT=3307
```

### **Step 5: Test Project Connection**

Run the connection tester:
```bash
node test-mysql-connection.js
```

This will tell you exactly what's working and what needs fixing.

## 🔍 Connection Flow in Your Project

### **1. Environment Variables (.env)**
```env
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=gym_management
```

### **2. Database Configuration (config/database.js)**
```javascript
const dbConfig = {
    host: process.env.DB_HOST,      // localhost
    user: process.env.DB_USER,      // root
    password: process.env.DB_PASSWORD, // your password
    database: process.env.DB_NAME,  // gym_management
    port: process.env.DB_PORT       // 3306
};
```

### **3. Connection Pool**
```javascript
const pool = mysql.createPool(dbConfig);
```

### **4. Usage in Routes**
```javascript
const [users] = await pool.execute('SELECT * FROM students');
```

## 🗄️ Database Schema

Your project automatically creates these tables:

```sql
gym_management/
├── students          # Student accounts
├── admins           # Administrator accounts
├── attendance       # Check-in/out records
├── classes          # Fitness classes
├── class_bookings   # Class reservations
├── facilities       # Gym facilities
└── facility_bookings # Facility reservations
```

## 🚀 Quick Setup Commands

```bash
# 1. Test MySQL connection
node test-mysql-connection.js

# 2. Install project dependencies
npm install

# 3. Create database and tables
npm run init-db

# 4. Start the application
npm run dev
```

## 🐛 Common Connection Issues

### **Issue 1: "ECONNREFUSED"**
**Problem**: MySQL server not running
**Solution**:
```bash
# Windows
net start mysql

# Mac
brew services start mysql

# Linux
sudo service mysql start
```

### **Issue 2: "ER_ACCESS_DENIED_ERROR"**
**Problem**: Wrong username/password
**Solutions**:
- Check `.env` file credentials
- Test manually: `mysql -u root -p`
- Reset MySQL password if needed

### **Issue 3: "ENOTFOUND"**
**Problem**: Wrong host
**Solution**:
- Use `localhost` for local MySQL
- Check if MySQL is installed

### **Issue 4: "ER_BAD_DB_ERROR"**
**Problem**: Database doesn't exist
**Solution**:
- Run `npm run init-db` to create database
- Database is auto-created, this is normal

## 🔒 Security Best Practices

### **1. Environment Variables**
- Never commit `.env` file to Git
- Use different passwords for production
- Limit database user permissions

### **2. Connection Pooling**
- Your project uses connection pooling
- Automatically manages connections
- Prevents connection leaks

### **3. SQL Injection Prevention**
- Uses parameterized queries
- All user input is sanitized
- Safe from SQL injection attacks

## 📊 Connection Monitoring

Your project includes connection monitoring:

```javascript
// Automatic connection testing
testConnection();

// Graceful shutdown
process.on('SIGINT', async () => {
    await pool.end();
    process.exit(0);
});
```

## 🔄 Development vs Production

### **Development (Local)**
```env
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_local_password
```

### **Production (Server)**
```env
DB_HOST=your_server_ip
DB_USER=production_user
DB_PASSWORD=strong_production_password
```

## 📞 Troubleshooting Checklist

- [ ] MySQL is installed
- [ ] MySQL service is running
- [ ] `.env` file has correct credentials
- [ ] Can connect manually: `mysql -u root -p`
- [ ] Firewall allows MySQL port (3306)
- [ ] Node.js dependencies installed: `npm install`

## 🎉 Success Indicators

You'll know the connection works when:
- ✅ `node test-mysql-connection.js` passes
- ✅ `npm run init-db` creates database
- ✅ Server starts with "Database connected successfully"
- ✅ Admin login works at http://localhost:3000/admin/dashboard.html

## 📖 Additional Resources

- **MySQL Documentation**: https://dev.mysql.com/doc/
- **Node.js MySQL2**: https://github.com/sidorares/node-mysql2
- **Environment Variables**: https://www.npmjs.com/package/dotenv

Your MySQL connection is now ready for your Gym Management System!
