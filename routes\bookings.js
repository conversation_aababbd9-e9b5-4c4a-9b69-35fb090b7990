const express = require('express');
const router = express.Router();
const { requireAny } = require('../middleware/auth');
const pool = require('../config/database');

// Get available classes
router.get('/available', requireAny, async (req, res) => {
    try {
        const [classes] = await pool.execute(`
            SELECT 
                c.*,
                (SELECT COUNT(*) FROM bookings 
                 WHERE class_id = c.id AND status = 'confirmed') as booked_slots,
                (c.capacity - (SELECT COUNT(*) FROM bookings 
                 WHERE class_id = c.id AND status = 'confirmed')) as available_slots
            FROM classes c
            WHERE c.start_time > NOW()
            ORDER BY c.start_time ASC
        `);

        res.json({ classes });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Book a class
router.post('/', requireAny, async (req, res) => {
    const connection = await pool.getConnection();
    try {
        await connection.beginTransaction();

        const { class_id } = req.body;
        const user_id = req.user.id;

        // Check if user already has a booking for this class
        const [existingBooking] = await connection.execute(
            'SELECT * FROM bookings WHERE user_id = ? AND class_id = ?',
            [user_id, class_id]
        );

        if (existingBooking.length > 0) {
            throw new Error('You have already booked this class');
        }

        // Check class capacity
        const [classDetails] = await connection.execute(
            `SELECT 
                capacity,
                (SELECT COUNT(*) FROM bookings 
                 WHERE class_id = ? AND status = 'confirmed') as current_bookings
            FROM classes WHERE id = ?`,
            [class_id, class_id]
        );

        if (!classDetails.length) {
            throw new Error('Class not found');
        }

        const status = classDetails[0].current_bookings >= classDetails[0].capacity 
            ? 'waitlist' 
            : 'confirmed';

        // Create booking
        await connection.execute(
            'INSERT INTO bookings (user_id, class_id, status) VALUES (?, ?, ?)',
            [user_id, class_id, status]
        );

        await connection.commit();
        res.json({ 
            message: status === 'confirmed' 
                ? 'Booking confirmed' 
                : 'Added to waiting list'
        });

    } catch (error) {
        await connection.rollback();
        res.status(400).json({ error: error.message });
    } finally {
        connection.release();
    }
});

// Get user's bookings
router.get('/my-bookings', requireAny, async (req, res) => {
    try {
        const [bookings] = await pool.execute(`
            SELECT 
                b.*,
                c.name as class_name,
                c.description,
                c.start_time,
                c.end_time,
                c.class_type
            FROM bookings b
            JOIN classes c ON b.class_id = c.id
            WHERE b.user_id = ?
            ORDER BY c.start_time ASC`,
            [req.user.id]
        );

        res.json({ bookings });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

module.exports = router;


