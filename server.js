const express = require('express');
const path = require('path');
const fs = require('fs');
const app = express();

// Serve static files from the root directory
app.use(express.static(path.join(__dirname)));

// Explicitly define routes for HTML pages
app.get('/login.html', (req, res) => {
  res.sendFile(path.join(__dirname, 'login.html'));
});

app.get('/registration.html', (req, res) => {
  res.sendFile(path.join(__dirname, 'registration.html'));
});

// API Routes
app.use('/api/auth', require('./routes/auth'));
app.use('/api/admin', require('./routes/admin'));
app.use('/api/bookings', require('./routes/bookings'));
app.use('/api/classes', require('./routes/classes'));
app.use('/api', require('./routes/test'));

// For any routes not explicitly defined, try to serve HTML files first
app.get('*', (req, res, next) => {
  const requestedPath = req.path;
  
  // If the path ends with .html, try to serve that file
  if (requestedPath.endsWith('.html')) {
    const filePath = path.join(__dirname, requestedPath);
    
    // Check if the file exists
    fs.access(filePath, fs.constants.F_OK, (err) => {
      if (err) {
        // File doesn't exist, continue to next middleware
        return next();
      }
      
      // File exists, serve it
      res.sendFile(filePath);
    });
  } else {
    // Not an HTML file request, continue to next middleware
    next();
  }
}, (req, res) => {
  // If no HTML file was found, serve index.html as fallback
  res.sendFile(path.join(__dirname, 'index.html'));
});

// Start the server
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});




