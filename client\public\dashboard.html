<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - University Gym</title>
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="dashboard-body">
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="images/logo.png" alt="UniGym Logo" class="logo">
                <h2>UniGym</h2>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li class="active">
                        <a href="#overview">
                            <i class="fas fa-home"></i>
                            <span>Overview</span>
                        </a>
                    </li>
                    <li>
                        <a href="#membership">
                            <i class="fas fa-id-card"></i>
                            <span>Membership</span>
                        </a>
                    </li>
                    <li>
                        <a href="#schedule">
                            <i class="fas fa-calendar-alt"></i>
                            <span>Class Schedule</span>
                        </a>
                    </li>
                    <li>
                        <a href="#workouts">
                            <i class="fas fa-dumbbell"></i>
                            <span>My Workouts</span>
                        </a>
                    </li>
                    <li>
                        <a href="#attendance">
                            <i class="fas fa-check-circle"></i>
                            <span>Attendance</span>
                        </a>
                    </li>
                    <li>
                        <a href="#profile">
                            <i class="fas fa-user"></i>
                            <span>Profile</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <div class="sidebar-footer">
                <button id="logoutBtn" onclick="handleLogout()">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </button>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Top Header -->
            <header class="dashboard-header">
                <div class="header-left">
                    <button id="menuToggle" class="menu-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1>Dashboard</h1>
                </div>
                <div class="header-right">
                    <div class="notifications">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </div>
                    <div class="user-profile">
                        <img src="images/default-avatar.png" alt="Profile" class="avatar">
                        <span id="userName">John Doe</span>
                    </div>
                </div>
            </header>

            <!-- Dashboard Sections -->
            <div class="dashboard-content">
                <!-- Overview Section -->
                <section id="overview" class="dashboard-section active">
                    <div class="welcome-banner">
                        <h2>Welcome back, <span id="userFullName">John</span>!</h2>
                        <p id="currentTime" class="current-time"></p>
                    </div>

                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-details">
                                <h3>Check-in Status</h3>
                                <p id="checkInStatus">Not Checked In</p>
                                <button id="checkInBtn" class="action-btn">
                                    Check In
                                </button>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <div class="stat-details">
                                <h3>Attendance Streak</h3>
                                <p id="attendanceStreak">5 days</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-fire"></i>
                            </div>
                            <div class="stat-details">
                                <h3>Today's Activity</h3>
                                <p id="todayActivity">45 minutes</p>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-grid">
                        <!-- Upcoming Classes -->
                        <div class="dashboard-card">
                            <h3>Upcoming Classes</h3>
                            <div id="upcomingClasses" class="class-list">
                                <!-- Will be populated by JavaScript -->
                            </div>
                        </div>

                        <!-- Activity Chart -->
                        <div class="dashboard-card">
                            <h3>Weekly Activity</h3>
                            <canvas id="activityChart"></canvas>
                        </div>

                        <!-- Quick Actions -->
                        <div class="dashboard-card">
                            <h3>Quick Actions</h3>
                            <div class="quick-actions">
                                <button onclick="bookClass()">
                                    <i class="fas fa-plus"></i> Book Class
                                </button>
                                <button onclick="viewSchedule()">
                                    <i class="fas fa-calendar"></i> View Schedule
                                </button>
                                <button onclick="updateProfile()">
                                    <i class="fas fa-user-edit"></i> Update Profile
                                </button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Other sections will be added here -->
            </div>
        </main>
    </div>

    <script src="js/dashboard.js"></script>
</body>
</html>