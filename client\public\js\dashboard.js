class Dashboard {
    constructor() {
        this.init();
    }

    async init() {
        try {
            // Check both session and local storage for auth
            const user = sessionStorage.getItem('user');
            const token = localStorage.getItem('token');

            if (!user && !token) {
                window.location.href = '/login.html';
                return;
            }

            this.userData = user ? JSON.parse(user) : null;
            
            // Initialize all dashboard components
            await this.loadUserData();
            this.setupEventListeners();
            this.initializeCharts();
            this.startTimeUpdates();
            this.setupSidebar();
        } catch (error) {
            console.error('Dashboard initialization failed:', error);
            this.showNotification('Failed to initialize dashboard', 'error');
        }
    }

    setupSidebar() {
        // Toggle sidebar on mobile
        const menuToggle = document.getElementById('menuToggle');
        const sidebar = document.querySelector('.sidebar');
        
        menuToggle?.addEventListener('click', () => {
            sidebar?.classList.toggle('active');
        });

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', (e) => {
            if (window.innerWidth <= 768 && 
                !e.target.closest('.sidebar') && 
                !e.target.closest('#menuToggle')) {
                sidebar?.classList.remove('active');
            }
        });
    }

    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.sidebar-nav a').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleNavigation(link);
            });
        });

        // Quick actions
        document.getElementById('checkInBtn')?.addEventListener('click', 
            () => this.handleCheckIn());
        document.getElementById('bookClassBtn')?.addEventListener('click', 
            () => this.handleBookClass());
        document.getElementById('startWorkoutBtn')?.addEventListener('click', 
            () => this.handleStartWorkout());

        // Logout
        document.getElementById('logoutBtn')?.addEventListener('click', 
            () => this.handleLogout());
    }

    async loadUserData() {
        try {
            const response = await fetch('/api/user/dashboard', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                credentials: 'include'
            });

            if (!response.ok) throw new Error('Failed to load user data');

            const data = await response.json();
            this.updateDashboardData(data);
        } catch (error) {
            this.showNotification('Failed to load dashboard data', 'error');
            throw error;
        }
    }

    updateDashboardData(data) {
        // Update user info
        document.getElementById('userFullName').textContent = data.fullName;
        document.getElementById('userName').textContent = data.firstName;

        // Update stats
        document.getElementById('todayWorkout').textContent = data.todayWorkout || '0h 0m';
        document.getElementById('caloriesBurned').textContent = `${data.caloriesBurned || 0} kcal`;
        document.getElementById('workoutStreak').textContent = `${data.streak || 0} days`;

        // Update check-in status
        const checkInBtn = document.getElementById('checkInBtn');
        if (data.checkedIn) {
            checkInBtn.disabled = true;
            checkInBtn.innerHTML = '<i class="fas fa-check"></i> Checked In';
            document.getElementById('checkInStatus').textContent = 'Checked In';
        }

        // Update upcoming classes
        this.updateUpcomingClasses(data.upcomingClasses || []);
    }

    updateUpcomingClasses(classes) {
        const container = document.getElementById('upcomingClasses');
        if (!container) return;

        if (classes.length === 0) {
            container.innerHTML = '<p class="no-classes">No upcoming classes</p>';
            return;
        }

        container.innerHTML = classes.map(cls => `
            <div class="class-item">
                <div class="class-time">
                    <i class="fas fa-clock"></i>
                    ${this.formatTime(cls.time)}
                </div>
                <div class="class-details">
                    <h4>${cls.name}</h4>
                    <p><i class="fas fa-user"></i> ${cls.instructor}</p>
                    <p><i class="fas fa-map-marker-alt"></i> ${cls.location}</p>
                </div>
            </div>
        `).join('');
    }

    initializeCharts() {
        const ctx = document.getElementById('activityChart')?.getContext('2d');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: this.getLast7Days(),
                datasets: [{
                    label: 'Activity Duration (minutes)',
                    data: [45, 60, 30, 75, 45, 60, 0],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    borderWidth: 2,
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    startTimeUpdates() {
        this.updateCurrentTime();
        setInterval(() => this.updateCurrentTime(), 60000);
    }

    updateCurrentTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit'
        });
        const dateString = now.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
        
        const timeElement = document.getElementById('currentTime');
        if (timeElement) {
            timeElement.textContent = `${dateString} ${timeString}`;
        }
    }

    async handleCheckIn() {
        try {
            const checkInBtn = document.getElementById('checkInBtn');
            checkInBtn.disabled = true;
            checkInBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking in...';

            const response = await fetch('/api/attendance/check-in', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                credentials: 'include'
            });

            if (!response.ok) throw new Error('Check-in failed');

            const data = await response.json();
            this.showNotification('Successfully checked in!', 'success');
            
            checkInBtn.innerHTML = '<i class="fas fa-check"></i> Checked In';
            document.getElementById('checkInStatus').textContent = 'Checked In';
            
            // Refresh dashboard data
            await this.loadUserData();
        } catch (error) {
            const checkInBtn = document.getElementById('checkInBtn');
            checkInBtn.disabled = false;
            checkInBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> Check In';
            this.showNotification('Failed to check in', 'error');
        }
    }

    async handleLogout() {
        try {
            await fetch('/api/auth/logout', {
                method: 'POST',
                credentials: 'include'
            });
        } finally {
            localStorage.clear();
            sessionStorage.clear();
            window.location.href = '/login.html';
        }
    }

    handleNavigation(link) {
        const targetId = link.getAttribute('href').substring(1);
        
        // Update active states
        document.querySelectorAll('.sidebar-nav li').forEach(li => {
            li.classList.remove('active');
        });
        link.parentElement.classList.add('active');

        // Show selected section
        document.querySelectorAll('.dashboard-section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(targetId)?.classList.add('active');
    }

    showNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
            ${message}
        `;
        document.body.appendChild(notification);
        setTimeout(() => notification.remove(), 3000);
    }

    getLast7Days() {
        return Array.from({length: 7}, (_, i) => {
            const date = new Date();
            date.setDate(date.getDate() - i);
            return date.toLocaleDateString('en-US', {weekday: 'short'});
        }).reverse();
    }

    formatTime(timeString) {
        return new Date(timeString).toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }
}

// Initialize dashboard
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new Dashboard();
});
