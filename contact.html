<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact - University Gym</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/forms.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        /* Contact page specific styles */
        .contact-section {
            padding: 80px 5% 60px;
            background-color: var(--light-gray);
        }
        
        .contact-section h1 {
            text-align: center;
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 2rem;
        }
        
        .contact-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .contact-info, .contact-form {
            background-color: var(--white);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .contact-info h2, .contact-form h2 {
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            font-size: 1.8rem;
            position: relative;
            padding-bottom: 10px;
        }
        
        .contact-info h2:after, .contact-form h2:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background-color: var(--secondary-color);
        }
        
        .info-item {
            margin-bottom: 1.5rem;
            padding-left: 10px;
            border-left: 3px solid var(--secondary-color);
        }
        
        .info-item h3 {
            font-size: 1.2rem;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        .info-item p {
            color: #666;
            line-height: 1.6;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--primary-color);
            font-weight: 500;
        }
        
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus, .form-group textarea:focus {
            border-color: var(--secondary-color);
            outline: none;
            box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2);
        }
        
        .submit-btn {
            background-color: var(--secondary-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.3s;
            width: 100%;
            font-weight: 600;
        }
        
        .submit-btn:hover {
            background-color: #c0392b;
            transform: translateY(-3px);
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
            .contact-container {
                grid-template-columns: 1fr;
            }
            
            .contact-section h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="logo">
                <h1>UniGym</h1>
            </div>
            <ul class="nav-links">
                <li><a href="index.html">Home</a></li>
                <li><a href="about.html">About</a></li>
                <li><a href="facilities.html">Facilities</a></li>
                <li><a href="contact.html">Contact</a></li>
               <!-- <li><a href="registration.html" class="btn-register">Register</a></li> -->
                <li><a href="login.html" class="btn-login">Login</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <section class="contact-section">
            <h1>Contact Us</h1>
            <div class="contact-container">
                <div class="contact-info">
                    <h2>Get in Touch</h2>
                    <div class="info-item">
                        <h3>Address</h3>
                        <p>Central University of Karnataka,<br>City: Gulabarg, State: Karnatak</p>
                    </div>
                    <div class="info-item">
                        <h3>Phone</h3>
                        <p>08477226714</p>
                    </div>
                    <div class="info-item">
                        <h3>Email</h3>
                        <p><EMAIL></p>
                    </div>
                    <div class="info-item">
                        <h3>Opening Hours</h3>
                        <p>Monday - Friday: 10:00 AM - 5:00 PM</p>
                    </div>
                </div>
                
                <div class="contact-form">
                    <h2>Send us a Message</h2>
                    <form id="contactForm" onsubmit="return validateContactForm()">
                        <div class="form-group">
                            <label for="name">Name*</label>
                            <input type="text" id="name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="email">Email*</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="subject">Subject*</label>
                            <input type="text" id="subject" name="subject" required>
                        </div>
                        <div class="form-group">
                            <label for="message">Message*</label>
                            <textarea id="message" name="message" rows="5" required></textarea>
                        </div>
                        <button type="submit" class="submit-btn">Send Message</button>
                    </form>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-section">
                <h3>Contact Info</h3>
                <p>Email: <EMAIL></p>
                <p>Phone: (*************</p>
            </div>
            <div class="footer-section">
                <h3>Opening Hours</h3>
                <p>Monday - Friday: 6:00 AM - 10:00 PM</p>
                <p>Saturday - Sunday: 7:00 AM - 8:00 PM</p>
            </div>
        </div>
    </footer>

    <script src="js/contact-validation.js"></script>
</body>
</html>

