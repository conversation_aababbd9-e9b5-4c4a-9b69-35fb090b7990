const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const pool = require('../config/database');

// Create a new class (admin/trainer only)
router.post('/', auth(['admin', 'trainer']), async (req, res) => {
    try {
        const {
            name,
            description,
            capacity,
            duration,
            start_time,
            end_time,
            trainer_id
        } = req.body;

        const [result] = await pool.execute(
            'INSERT INTO classes (name, description, capacity, duration, start_time, end_time, trainer_id) VALUES (?, ?, ?, ?, ?, ?, ?)',
            [name, description, capacity, duration, start_time, end_time, trainer_id]
        );

        res.status(201).json({
            success: true,
            message: 'Class created successfully',
            classId: result.insertId
        });

    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Get all classes with filters
router.get('/', async (req, res) => {
    try {
        const { date, trainer, type } = req.query;
        let query = 'SELECT c.*, u.full_name as trainer_name FROM classes c LEFT JOIN users u ON c.trainer_id = u.id WHERE 1=1';
        const params = [];

        if (date) {
            query += ' AND DATE(c.start_time) = ?';
            params.push(date);
        }

        if (trainer) {
            query += ' AND c.trainer_id = ?';
            params.push(trainer);
        }

        if (type) {
            query += ' AND c.type = ?';
            params.push(type);
        }

        const [classes] = await pool.execute(query, params);
        res.json({ classes });

    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Update a class
router.put('/:id', auth(['admin', 'trainer']), async (req, res) => {
    try {
        const { id } = req.params;
        const {
            name,
            description,
            capacity,
            duration,
            start_time,
            end_time
        } = req.body;

        await pool.execute(
            'UPDATE classes SET name = ?, description = ?, capacity = ?, duration = ?, start_time = ?, end_time = ? WHERE id = ?',
            [name, description, capacity, duration, start_time, end_time, id]
        );

        res.json({ message: 'Class updated successfully' });

    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Delete a class
router.delete('/:id', auth(['admin']), async (req, res) => {
    try {
        const { id } = req.params;
        await pool.execute('DELETE FROM classes WHERE id = ?', [id]);
        res.json({ message: 'Class deleted successfully' });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

module.exports = router;

