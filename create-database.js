const mysql = require('mysql2/promise');
require('dotenv').config();

async function createDatabase() {
    let connection;
    
    try {
        console.log('🔄 Connecting to MySQL server...');
        
        // Connect to MySQL server (without specifying database)
        connection = await mysql.createConnection({
            host: process.env.DB_HOST || 'localhost',
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || '',
            port: process.env.DB_PORT || 3306
        });
        
        console.log('✅ Connected to MySQL server');
        
        // Create database
        console.log('🔄 Creating gym_management database...');
        await connection.execute('CREATE DATABASE IF NOT EXISTS gym_management');
        console.log('✅ Database gym_management created successfully!');
        
        // Use the database
        await connection.execute('USE gym_management');
        console.log('✅ Using gym_management database');
        
        console.log('🎉 Database setup completed! You can now run: npm run init-db');
        
    } catch (error) {
        console.error('❌ Database creation failed:', error.message);
        
        if (error.code === 'ER_ACCESS_DENIED_ERROR') {
            console.error('🔧 Please check your database credentials in .env file');
        } else if (error.code === 'ECONNREFUSED') {
            console.error('🔧 Please make sure MySQL server is running');
        }
        
        process.exit(1);
    } finally {
        if (connection) {
            await connection.end();
            console.log('🔌 Database connection closed');
        }
    }
}

// Run the creation
createDatabase();
