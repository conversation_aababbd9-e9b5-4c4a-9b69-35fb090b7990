<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - University Gym</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        .reset-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            padding: 20px;
        }
        
        .reset-box {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }
        
        .reset-logo {
            display: block;
            margin: 0 auto 20px;
            max-width: 150px;
            height: auto;
        }
        
        .reset-box h2 {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
        }
        
        .reset-box p {
            text-align: center;
            color: #666;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="reset-box">
            <img src="images/cuk-full-logo.png" alt="University GYM Logo" class="reset-logo">
            <h2>Reset Password</h2>
            <p>Enter your email address and we'll send you instructions to reset your password.</p>
            
            <form id="resetForm">
                <div class="form-group">
                    <label for="studentId">Student ID</label>
                    <input type="text" id="studentId" name="studentId" required placeholder="Enter your student ID">
                </div>
                
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" id="email" name="email" required placeholder="Enter your registered email">
                    <small>Please enter the email address associated with your student account</small>
                </div>
                
                <div id="errorMessage" class="error-message"></div>
                <div id="successMessage" class="success-message"></div>
                
                <button type="submit" class="login-btn" id="resetBtn">
                    Send Reset Link
                </button>
                
                <div class="login-footer" style="margin-top: 20px; text-align: center;">
                    <a href="login.html">Back to Login</a>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const resetForm = document.getElementById('resetForm');
            const resetBtn = document.getElementById('resetBtn');
            const errorMessage = document.getElementById('errorMessage');
            const successMessage = document.getElementById('successMessage');
            
            resetForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                // Hide any existing messages
                errorMessage.style.display = 'none';
                successMessage.style.display = 'none';
                
                // Show loading state
                resetBtn.disabled = true;
                resetBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
                
                const studentId = document.getElementById('studentId').value.trim();
                const email = document.getElementById('email').value.trim();
                
                try {
                    const response = await fetch('/api/auth/forgot-password', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ student_id: studentId, email })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        successMessage.textContent = 'Password reset instructions have been sent to your email.';
                        successMessage.style.display = 'block';
                        resetForm.reset();
                    } else {
                        errorMessage.textContent = data.message || 'Failed to send reset link. Please try again.';
                        errorMessage.style.display = 'block';
                    }
                } catch (error) {
                    console.error('Error:', error);
                    errorMessage.textContent = 'An error occurred. Please try again later.';
                    errorMessage.style.display = 'block';
                } finally {
                    // Reset button state
                    resetBtn.disabled = false;
                    resetBtn.innerHTML = 'Send Reset Link';
                }
            });
        });
    </script>
</body>
</html>



