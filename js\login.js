class AuthManager {
    constructor() {
        this.loginForm = document.getElementById('loginForm');
        this.errorDiv = document.getElementById('errorMessage');
        this.loginButton = document.querySelector('.login-btn');
        
        this.setupEventListeners();
    }

    setupEventListeners() {
        this.loginForm?.addEventListener('submit', (e) => this.handleLogin(e));
        document.addEventListener('DOMContentLoaded', () => this.checkAuthStatus());
    }

    async handleLogin(event) {
        event.preventDefault();
        
        // Get form data
        const studentId = document.getElementById('studentId').value.trim();
        const password = document.getElementById('password').value;

        // Create credentials object
        const credentials = {
            student_id: studentId,
            password: password
        };

        if (!this.validateInputs(credentials)) {
            return;
        }

        try {
            await this.performLogin(credentials);
        } catch (error) {
            this.handleError(error);
        }
    }

    validateInputs(credentials) {
        if (!credentials.student_id || !credentials.password) {
            this.showError('Please fill in all fields');
            return false;
        }
        if (credentials.student_id.length < 5) {
            this.showError('Student ID must be at least 5 characters');
            return false;
        }
        if (credentials.password.length < 6) {
            this.showError('Password must be at least 6 characters');
            return false;
        }
        return true;
    }

    async performLogin(credentials) {
        this.setLoadingState(true);

        try {
            console.log('Attempting login with:', { student_id: credentials.student_id, passwordLength: credentials.password?.length });
            
            const response = await fetch('/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(credentials),
                credentials: 'include'
            });

            // First check if response exists
            if (!response) {
                throw new Error('No response from server');
            }

            console.log('Server response status:', response.status);

            // Try to get the response text first
            const responseText = await response.text();
            console.log('Raw response:', responseText);

            // Try to parse it as JSON
            let data;
            try {
                data = responseText ? JSON.parse(responseText) : {};
            } catch (e) {
                console.error('JSON Parse Error:', e);
                console.log('Raw Response:', responseText);
                throw new Error('Invalid server response format');
            }

            if (!response.ok) {
                throw new Error(data.message || 'Login failed');
            }

            // Store auth data
            if (data.token) {
                localStorage.setItem('token', data.token);
            }
            
            if (data.user) {
                sessionStorage.setItem('user', JSON.stringify({
                    id: data.user.id,
                    student_id: data.user.student_id,
                    role: data.user.role,
                    full_name: data.user.full_name
                }));
            }

            // Redirect based on role
            window.location.href = data.user?.role === 'admin' 
                ? '/admin/dashboard.html' 
                : '/dashboard.html';

        } catch (error) {
            this.showError(error.message || 'Login failed. Please try again.');
            console.error('Login error:', error);
        } finally {
            this.setLoadingState(false);
        }
    }

    setLoadingState(isLoading) {
        this.loginButton.disabled = isLoading;
        this.loginButton.innerHTML = isLoading 
            ? '<i class="fas fa-spinner fa-spin"></i> Logging in...'
            : 'Login';
    }

    showError(message) {
        if (this.errorDiv) {
            this.errorDiv.textContent = message;
            this.errorDiv.style.display = 'block';
            
            setTimeout(() => {
                this.errorDiv.style.display = 'none';
            }, 5000);
        } else {
            console.error('Error element not found');
            alert(message);
        }
    }

    handleError(error) {
        this.showError(error.message || 'An error occurred during login');
        console.error('Login error:', error);
    }

    checkAuthStatus() {
        const user = sessionStorage.getItem('user');
        const token = localStorage.getItem('token');
        
        if (user && token) {
            const userData = JSON.parse(user);
            const redirectPath = userData.role === 'admin' 
                ? '/admin/dashboard.html' 
                : '/dashboard.html';
            window.location.href = redirectPath;
        }
    }
}

// Initialize auth manager
const authManager = new AuthManager();

// Toggle password visibility
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.querySelector('.toggle-password');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    }
}

// Show error message
function showError(message) {
    const errorDiv = document.getElementById('errorMessage');
    errorDiv.textContent = message;
    errorDiv.style.display = 'block';
}

// Hide error message
function hideError() {
    const errorDiv = document.getElementById('errorMessage');
    errorDiv.style.display = 'none';
}

// Check if user is already logged in
document.addEventListener('DOMContentLoaded', () => {
    const token = localStorage.getItem('token');
    if (token) {
        window.location.href = '/dashboard.html';
    }
});

// Add this testing function to your login.js
// Call it from browser console: testLoginFeatures()
async function testLoginFeatures() {
    const tests = {
        validation: {
            emptyFields: false,
            shortStudentId: false,
            shortPassword: false,
            validInput: false
        },
        security: {
            passwordToggle: false,
            formSubmission: false,
            corsHeaders: false
        },
        ux: {
            loadingState: false,
            errorMessages: false,
            redirects: false
        },
        storage: {
            tokenStorage: false,
            sessionStorage: false
        }
    };

    try {
        // Test validation
        const authManager = new AuthManager();
        
        // Empty fields test
        await authManager.handleLogin(new Event('submit'));
        tests.validation.emptyFields = document.getElementById('errorMessage').textContent.includes('fill in all fields');

        // Short student ID test
        const shortIdEvent = new Event('submit');
        shortIdEvent.target = { 
            elements: { 
                studentId: { value: '123' },
                password: { value: 'validpass123' }
            }
        };
        await authManager.handleLogin(shortIdEvent);
        tests.validation.shortStudentId = document.getElementById('errorMessage').textContent.includes('at least 5 characters');

        // Short password test
        const shortPassEvent = new Event('submit');
        shortPassEvent.target = { 
            elements: { 
                studentId: { value: '12345' },
                password: { value: '123' }
            }
        };
        await authManager.handleLogin(shortPassEvent);
        tests.validation.shortPassword = document.getElementById('errorMessage').textContent.includes('Password must be');

        // Valid input test
        tests.validation.validInput = authManager.validateInputs({
            student_id: '12345',
            password: 'validpass123'
        });

        // Security tests
        tests.security.passwordToggle = typeof togglePassword === 'function';
        tests.security.formSubmission = authManager.loginForm !== null;
        
        // Test CORS headers
        const headers = await fetch('/api/auth/login', {
            method: 'HEAD',
            credentials: 'include'
        });
        tests.security.corsHeaders = headers.ok;

        // UX tests
        tests.ux.loadingState = authManager.loginButton !== null;
        tests.ux.errorMessages = authManager.errorDiv !== null;
        tests.ux.redirects = typeof authManager.checkAuthStatus === 'function';

        // Storage tests
        tests.storage.tokenStorage = localStorage.getItem('token') === null;
        tests.storage.sessionStorage = sessionStorage.getItem('user') === null;

        console.table(tests);
        return tests;

    } catch (error) {
        console.error('Test failed:', error);
        return tests;
    }
}

// Test function to check server response
async function testLoginRequest() {
    try {
        const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                student_id: "12345",
                password: "password123"
            }),
            credentials: 'include'
        });

        const responseText = await response.text();
        console.log('Raw response:', responseText);
        
        try {
            const data = JSON.parse(responseText);
            console.log('Parsed data:', data);
        } catch (e) {
            console.error('Failed to parse JSON:', e);
        }
        
    } catch (error) {
        console.error('Request failed:', error);
    }
}

// Run this in console
// testLoginRequest();


