const jwt = require('jsonwebtoken');
const pool = require('../config/database');

// Authentication middleware
const authenticateToken = (requiredRoles = []) => {
    return async (req, res, next) => {
        try {
            // Get token from header or cookie
            let token = req.headers.authorization?.split(' ')[1]; // Bearer token
            
            if (!token) {
                token = req.cookies?.token; // Cookie token
            }
            
            if (!token) {
                return res.status(401).json({ 
                    success: false, 
                    message: 'Access denied. No token provided.' 
                });
            }
            
            // Verify token
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            
            // Get user details from database
            let user;
            if (decoded.role === 'admin' || decoded.role === 'super_admin') {
                const [admins] = await pool.execute(
                    'SELECT id, username, email, full_name, role FROM admins WHERE id = ?',
                    [decoded.id]
                );
                user = admins[0];
            } else {
                const [students] = await pool.execute(
                    'SELECT id, student_id, full_name, email, role, status FROM students WHERE id = ?',
                    [decoded.id]
                );
                user = students[0];
            }
            
            if (!user) {
                return res.status(401).json({ 
                    success: false, 
                    message: 'Invalid token. User not found.' 
                });
            }
            
            // Check if user is active (for students)
            if (user.status && user.status !== 'active') {
                return res.status(403).json({ 
                    success: false, 
                    message: 'Account is inactive or suspended.' 
                });
            }
            
            // Check role permissions
            if (requiredRoles.length > 0 && !requiredRoles.includes(user.role)) {
                return res.status(403).json({ 
                    success: false, 
                    message: 'Access denied. Insufficient permissions.' 
                });
            }
            
            // Add user to request object
            req.user = user;
            next();
            
        } catch (error) {
            if (error.name === 'JsonWebTokenError') {
                return res.status(401).json({ 
                    success: false, 
                    message: 'Invalid token.' 
                });
            } else if (error.name === 'TokenExpiredError') {
                return res.status(401).json({ 
                    success: false, 
                    message: 'Token expired.' 
                });
            } else {
                console.error('Auth middleware error:', error);
                return res.status(500).json({ 
                    success: false, 
                    message: 'Internal server error.' 
                });
            }
        }
    };
};

// Optional authentication (doesn't fail if no token)
const optionalAuth = async (req, res, next) => {
    try {
        let token = req.headers.authorization?.split(' ')[1];
        
        if (!token) {
            token = req.cookies?.token;
        }
        
        if (token) {
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            
            let user;
            if (decoded.role === 'admin' || decoded.role === 'super_admin') {
                const [admins] = await pool.execute(
                    'SELECT id, username, email, full_name, role FROM admins WHERE id = ?',
                    [decoded.id]
                );
                user = admins[0];
            } else {
                const [students] = await pool.execute(
                    'SELECT id, student_id, full_name, email, role, status FROM students WHERE id = ?',
                    [decoded.id]
                );
                user = students[0];
            }
            
            if (user && (!user.status || user.status === 'active')) {
                req.user = user;
            }
        }
        
        next();
    } catch (error) {
        // Ignore auth errors in optional auth
        next();
    }
};

// Role-based access control helpers
const requireAdmin = authenticateToken(['admin', 'super_admin']);
const requireStudent = authenticateToken(['student']);
const requireAny = authenticateToken(['student', 'admin', 'super_admin']);

module.exports = {
    authenticateToken,
    optionalAuth,
    requireAdmin,
    requireStudent,
    requireAny
};
