/* Dashboard Layout */
.dashboard-body {
    margin: 0;
    padding: 0;
    background: #f4f6f9;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.dashboard-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Styles */
.sidebar {
    width: 260px;
    background: #1a237e;
    color: white;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    position: fixed;
    height: 100vh;
}

.sidebar-header {
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.dashboard-logo {
    width: 40px;
    height: 40px;
}

.sidebar-nav {
    padding: 1rem 0;
    flex-grow: 1;
}

.sidebar-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-nav li {
    margin: 0.5rem 0;
}

.sidebar-nav a {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    padding: 0.75rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.sidebar-nav a:hover,
.sidebar-nav li.active a {
    background: rgba(255,255,255,0.1);
    color: white;
}

.sidebar-nav i {
    width: 20px;
    text-align: center;
}

/* Main Content */
.main-content {
    flex-grow: 1;
    margin-left: 260px;
    padding: 2rem;
}

/* Dashboard Header */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    background: white;
    padding: 1rem 2rem;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

/* Quick Stats */
.quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-card i {
    font-size: 2rem;
    color: #1a237e;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.dashboard-card {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.action-btn {
    background: #1a237e;
    color: white;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: #283593;
}

/* Activity Feed */
.activity-feed {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid #eee;
}

/* Profile Section */
.profile-container {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.profile-header {
    display: flex;
    align-items: center;
    gap: 2rem;
    margin-bottom: 2rem;
}

.profile-avatar-container {
    position: relative;
}

.change-avatar-btn {
    position: absolute;
    bottom: 0;
    right: 0;
    background: #1a237e;
    color: white;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }
}

/* Attendance Styles */
.attendance-container {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.attendance-filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.attendance-filters select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.attendance-table {
    overflow-x: auto;
}

.attendance-table table {
    width: 100%;
    border-collapse: collapse;
}

.attendance-table th,
.attendance-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.attendance-table th {
    background-color: #f5f5f5;
    font-weight: 600;
}

.attendance-table tr:hover {
    background-color: #f9f9f9;
}

/* Schedule Styles */
.schedule-container {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.schedule-filters {
    margin-bottom: 2rem;
}

.schedule-filters select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    min-width: 200px;
}

.schedule-grid {
    display: grid;
    gap: 1.5rem;
}

.schedule-day {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
}

.schedule-day h3 {
    margin-bottom: 1rem;
    color: #2196F3;
}

.schedule-slots {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.class-slot {
    background: white;
    padding: 1rem;
    border-radius: 5px;
    border-left: 4px solid #2196F3;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.class-slot h4 {
    margin: 0;
    color: #333;
}

.class-slot p {
    margin: 0.5rem 0 0;
    color: #666;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: -250px;
        height: 100vh;
        z-index: 1000;
    }

    .sidebar.active {
        left: 0;
    }

    .sidebar-toggle {
        display: block;
    }
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}




