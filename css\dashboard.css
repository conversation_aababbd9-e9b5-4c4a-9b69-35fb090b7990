:root {
    --primary-color: #1a237e;
    --secondary-color: #e74c3c;
    --accent-color: #3498db;
    --light-bg: #f5f8fa;
    --dark-text: #2c3e50;
    --light-text: #ecf0f1;
    --border-radius: 10px;
    --card-shadow: 0 4px 12px rgba(0,0,0,0.1);
    --transition-speed: 0.3s;
}

/* Dashboard Layout */
.dashboard-body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background-color: var(--light-bg);
    color: var(--dark-text);
}

.dashboard-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Styles */
.sidebar {
    width: 250px;
    background-color: var(--primary-color);
    color: var(--light-text);
    display: flex;
    flex-direction: column;
    transition: all var(--transition-speed) ease;
    z-index: 100;
}

.sidebar-header {
    padding: 20px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dashboard-logo {
    width: 40px;
    height: 40px;
    margin-right: 10px;
}

.sidebar-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.sidebar-nav {
    flex: 1;
    padding: 20px 0;
}

.sidebar-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-nav li {
    margin-bottom: 5px;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--light-text);
    text-decoration: none;
    transition: all var(--transition-speed) ease;
}

.sidebar-nav a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.sidebar-nav li.active a,
.sidebar-nav a:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-left: 4px solid var(--accent-color);
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.logout-btn {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 12px;
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--light-text);
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: all var(--transition-speed) ease;
}

.logout-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.logout-btn i {
    margin-right: 10px;
}

/* Main Content Styles */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
}

/* Header Styles */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 30px;
    background-color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    z-index: 10;
}

.header-left {
    display: flex;
    align-items: center;
}

.menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--dark-text);
    cursor: pointer;
    margin-right: 15px;
}

.header-left h1 {
    margin: 0;
    font-size: 1.5rem;
    color: var(--dark-text);
}

.header-right {
    display: flex;
    align-items: center;
}

/* Notifications Dropdown */
.notifications-dropdown {
    position: relative;
    margin-right: 20px;
}

.notifications-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--dark-text);
    cursor: pointer;
    position: relative;
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: var(--secondary-color);
    color: white;
    font-size: 0.7rem;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dropdown-content {
    display: none;
    position: absolute;
    right: 0;
    top: 100%;
    width: 300px;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    z-index: 100;
    padding: 15px;
    margin-top: 10px;
}

.notifications-dropdown.active .dropdown-content {
    display: block;
}

.dropdown-content h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.1rem;
    color: var(--dark-text);
}

.notification-item {
    display: flex;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.notification-icon {
    margin-right: 15px;
    color: var(--accent-color);
    font-size: 1.2rem;
}

.notification-text p {
    margin: 0 0 5px 0;
    font-size: 0.9rem;
}

.notification-time {
    font-size: 0.8rem;
    color: #888;
}

.view-all {
    display: block;
    text-align: center;
    margin-top: 15px;
    color: var(--accent-color);
    text-decoration: none;
    font-size: 0.9rem;
}

/* User Info Dropdown */
.user-info {
    display: flex;
    align-items: center;
    cursor: pointer;
    position: relative;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-left: 10px;
}

.user-dropdown {
    display: none;
    position: absolute;
    right: 0;
    top: 100%;
    width: 200px;
    background-color: white
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }
}

/* Attendance Styles */
.attendance-container {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.attendance-filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.attendance-filters select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.attendance-table {
    overflow-x: auto;
}

.attendance-table table {
    width: 100%;
    border-collapse: collapse;
}

.attendance-table th,
.attendance-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.attendance-table th {
    background-color: #f5f5f5;
    font-weight: 600;
}

.attendance-table tr:hover {
    background-color: #f9f9f9;
}

/* Schedule Styles */
.schedule-container {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.schedule-filters {
    margin-bottom: 2rem;
}

.schedule-filters select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    min-width: 200px;
}

.schedule-grid {
    display: grid;
    gap: 1.5rem;
}

.schedule-day {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
}

.schedule-day h3 {
    margin-bottom: 1rem;
    color: #2196F3;
}

.schedule-slots {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.class-slot {
    background: white;
    padding: 1rem;
    border-radius: 5px;
    border-left: 4px solid #2196F3;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.class-slot h4 {
    margin: 0;
    color: #333;
}

.class-slot p {
    margin: 0.5rem 0 0;
    color: #666;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: -250px;
        height: 100vh;
        z-index: 1000;
    }

    .sidebar.active {
        left: 0;
    }

    .sidebar-toggle {
        display: block;
    }
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer class */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}

.logout-btn i {
    font-size: 1.1rem;
}

.logout-btn:active {
    transform: scale(0.98);
}

/* Add this to the existing sidebar-footer



