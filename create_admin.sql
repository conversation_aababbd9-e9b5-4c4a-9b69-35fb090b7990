-- Drop the table if it exists and recreate it
DROP TABLE IF EXISTS admins;

CREATE TABLE admins (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create a new admin user with password "admin123"
-- Note: Run this command through your Node.js application to properly hash the password
INSERT INTO admins (username, password) 
VALUES ('admin', '$2b$10$5QFB6jR6qP2wN9yPQQXOXO9VFT.I7CJsCK4UH1eGVLyhFP4aRfOdm');

