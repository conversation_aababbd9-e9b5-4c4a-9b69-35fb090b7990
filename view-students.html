<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Students - University Gym</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        .students-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 2rem;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .students-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 2rem;
        }
        
        .students-table th,
        .students-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .students-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .students-table tr:hover {
            background-color: #f5f5f5;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }
        
        .error {
            color: #dc3545;
            text-align: center;
            padding: 2rem;
        }
        
        .refresh-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 1rem;
        }
        
        .refresh-btn:hover {
            background: #0056b3;
        }
        
        .student-count {
            background: #e9ecef;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="logo">
                <img src="images/cuk-full-logo.png" alt="UniGym Logo" class="nav-logo">
                <span>University GYM</span>
            </div>
            <ul class="nav-links">
                <li><a href="index.html">Home</a></li>
                <li><a href="admin/dashboard.html">Admin Dashboard</a></li>
                <li><a href="view-students.html" class="active">View Students</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <div class="students-container">
            <h1><i class="fas fa-users"></i> Registered Students</h1>
            
            <button class="refresh-btn" onclick="loadStudents()">
                <i class="fas fa-sync-alt"></i> Refresh Data
            </button>
            
            <div class="student-count" id="studentCount">
                Loading student count...
            </div>
            
            <div id="studentsContent">
                <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i> Loading students...
                </div>
            </div>
        </div>
    </main>

    <script>
        async function loadStudents() {
            const content = document.getElementById('studentsContent');
            const countDiv = document.getElementById('studentCount');
            
            // Show loading
            content.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Loading students...</div>';
            countDiv.textContent = 'Loading student count...';
            
            try {
                const response = await fetch('/api/admin/students');
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                if (data.success) {
                    const students = data.students;
                    countDiv.innerHTML = `<i class="fas fa-users"></i> Total Students: ${students.length}`;
                    
                    if (students.length === 0) {
                        content.innerHTML = '<div class="error">No students registered yet.</div>';
                        return;
                    }
                    
                    // Create table
                    let tableHTML = `
                        <table class="students-table">
                            <thead>
                                <tr>
                                    <th>Student ID</th>
                                    <th>Full Name</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <th>Gender</th>
                                    <th>Department</th>
                                    <th>Course</th>
                                    <th>Year</th>
                                    <th>Registration Date</th>
                                </tr>
                            </thead>
                            <tbody>
                    `;
                    
                    students.forEach(student => {
                        const regDate = new Date(student.created_at).toLocaleDateString();
                        tableHTML += `
                            <tr>
                                <td>${student.student_id}</td>
                                <td>${student.full_name}</td>
                                <td>${student.email}</td>
                                <td>${student.phone}</td>
                                <td>${student.gender}</td>
                                <td>${student.department}</td>
                                <td>${student.course}</td>
                                <td>${student.year}</td>
                                <td>${regDate}</td>
                            </tr>
                        `;
                    });
                    
                    tableHTML += '</tbody></table>';
                    content.innerHTML = tableHTML;
                    
                } else {
                    content.innerHTML = `<div class="error">Error: ${data.message}</div>`;
                }
                
            } catch (error) {
                console.error('Error loading students:', error);
                content.innerHTML = `<div class="error">Failed to load students: ${error.message}</div>`;
            }
        }
        
        // Load students when page loads
        document.addEventListener('DOMContentLoaded', loadStudents);
    </script>
</body>
</html>
