require('dotenv').config();
const mysql = require('mysql2/promise');
const bcrypt = require('bcrypt');

async function createAdmin() {
    const pool = mysql.createPool({
        host: process.env.DB_HOST,
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME
    });

    try {
        const hashedPassword = await bcrypt.hash('admin123', 10);
        const connection = await pool.getConnection();
        
        await connection.execute(
            'INSERT INTO admins (username, password) VALUES (?, ?)',
            ['admin', hashedPassword]
        );
        
        console.log('Admin account created successfully');
        connection.release();
    } catch (error) {
        console.error('Error creating admin:', error);
    } finally {
        await pool.end();
    }
}

createAdmin();
