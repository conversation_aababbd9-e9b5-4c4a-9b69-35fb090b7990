.registration-container {
    padding: 120px 5% 60px;
    min-height: 100vh;
    background-color: var(--light-gray);
}

.registration-form {
    max-width: 800px;
    margin: 0 auto;
    background-color: var(--white);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

.registration-form h2 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: 2rem;
}

.progress-bar {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
    position: relative;
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #ddd;
    z-index: 1;
}

.step {
    background-color: var(--white);
    color: #999;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    position: relative;
    z-index: 2;
}

.step.active {
    background-color: var(--secondary-color);
    color: var(--white);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-color);
    font-weight: 500;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="tel"],
.form-group input[type="password"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.radio-group {
    display: flex;
    gap: 2rem;
}

.radio-group input[type="radio"] {
    margin-right: 0.5rem;
}

.btn-prev,
.btn-next,
.btn-submit {
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.3s;
}

.btn-prev {
    background-color: #ddd;
    color: var(--text-color);
    margin-right: 1rem;
}

.btn-next,
.btn-submit {
    background-color: var(--secondary-color);
    color: var(--white);
    float: right;
}

.btn-prev:hover,
.btn-next:hover,
.btn-submit:hover {
    opacity: 0.9;
}

/* Error states */
.form-group.error input {
    border-color: #e74c3c;
}

.error-message {
    color: #e74c3c;
    font-size: 0.9rem;
    margin-top: 0.3rem;
}

@media (max-width: 768px) {
    .registration-form {
        padding: 1rem;
    }

    .progress-bar {
        font-size: 0.8rem;
    }

    .btn-prev,
    .btn-next,
    .btn-submit {
        width: 100%;
        margin: 0.5rem 0;
        float: none;
    }
}

/* Add these new styles to your existing CSS */

.password-input {
    position: relative;
    width: 100%;
}

.password-input input {
    width: 100%;
    padding-right: 40px;
}

.toggle-password {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #666;
}

.terms-checkbox {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 20px 0;
}

.terms-checkbox input[type="checkbox"] {
    width: auto;
}

.btn-prev i, .btn-next i {
    margin: 0 5px;
}

.step i {
    margin-right: 5px;
}

/* Error state improvements */
.form-group.error input,
.form-group.error select {
    border-color: #e74c3c;
}

.error-message {
    color: #e74c3c;
    font-size: 0.9rem;
    margin-top: 5px;
    display: none;
}

.form-group.error .error-message {
    display: block;
}

/* Success state */
.form-group.success input,
.form-group.success select {
    border-color: #2ecc71;
}

