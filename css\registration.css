/* Registration Hero Section */
.registration-hero {
    background: linear-gradient(rgba(26, 35, 126, 0.7), rgba(0, 0, 0, 0.8)),
                url('../images/hero/gym-hero-background.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: var(--white);
    margin-top: 60px;
    position: relative;
}

.registration-hero:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, transparent 0%, rgba(0,0,0,0.4) 100%);
    z-index: 1;
}

.registration-hero .hero-content {
    max-width: 800px;
    padding: 0 20px;
    position: relative;
    z-index: 2;
    animation: fadeInUp 1.2s ease;
}

.registration-hero h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 2px;
    text-shadow: 0 2px 10px rgba(0,0,0,0.3);
    font-weight: 800;
}

.registration-hero p {
    font-size: 1.2rem;
    opacity: 0.9;
    text-shadow: 0 2px 5px rgba(0,0,0,0.3);
}

/* Registration Form Container */
.registration-container {
    max-width: 1200px;
    margin: 50px auto;
    padding: 0 20px;
}

.registration-form-container {
    background-color: var(--white);
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    padding: 40px;
    margin-bottom: 50px;
}

.registration-form-container h2 {
    text-align: center;
    margin-bottom: 30px;
    color: var(--primary-color);
    font-size: 2rem;
}

/* Progress Indicator */
.form-progress {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    padding: 0 20px;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 1;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e0e0e0;
    color: #757575;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.step-label {
    font-size: 0.85rem;
    color: #757575;
    text-align: center;
    transition: all 0.3s ease;
}

.progress-line {
    flex: 1;
    height: 3px;
    background-color: #e0e0e0;
    margin: 0 10px;
    position: relative;
    top: -20px;
}

.progress-step.active .step-number {
    background-color: var(--primary-color);
    color: white;
}

.progress-step.active .step-label {
    color: var(--primary-color);
    font-weight: 600;
}

.progress-step.completed .step-number {
    background-color: #4caf50;
    color: white;
}

/* Multi-step Form */
.form-step {
    display: none;
}

.form-step.active {
    display: block;
    animation: fadeIn 0.5s ease;
}

.form-step h3 {
    margin-bottom: 25px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--light-gray);
    color: var(--primary-color);
}

/* Form Groups */
.form-group {
    margin-bottom: 25px;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--dark-gray);
}

.form-group small {
    display: block;
    margin-top: 5px;
    color: var(--medium-gray);
    font-size: 0.85rem;
    line-height: 1.4;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Input field styling */
.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="tel"],
.form-group input[type="number"],
.form-group input[type="password"],
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--light-gray);
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    color: var(--dark-gray);
    background-color: var(--white);
    box-sizing: border-box;
}

/* Ensure placeholder text stays within the input field */
.form-group input::placeholder {
    color: #aaa;
    opacity: 0.7;
}

/* Textarea specific styling */
.form-group textarea {
    background-color: #e4ffe1;  /* Light blue background */
    color: #1a3c6e;             /* Darker blue text */
    border-color: #b8d0e8;      /* Medium blue border */
    resize: vertical;
    min-height: 100px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.form-group textarea:focus {
    background-color: #e4ffe1;  /* Slightly darker blue when focused */
    border-color: var(--primary-color);
    color: #0a2b5c;             /* Even darker blue text when focused */
    box-shadow: 0 0 0 3px rgba(26, 35, 126, 0.15);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(26, 35, 126, 0.2);
}

/* Radio and Checkbox Groups */
.radio-group, .checkbox-group {
    display: flex;
    gap: 20px;
    margin-top: 5px;
}

.radio-group label, .checkbox-group label {
    display: flex;
    align-items: center;
    font-weight: normal;
    cursor: pointer;
}

.radio-group input, .checkbox-group input {
    margin-right: 8px;
}

/* Form Buttons */
.form-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
}

.btn {
    padding: 12px 25px;
    border: none;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.prev-btn {
    background-color: var(--light-gray);
    color: var(--dark-gray);
}

.prev-btn:hover {
    background-color: var(--medium-gray);
}

.next-btn, .submit-btn {
    background-color: var(--primary-color);
    color: var(--white);
}

.next-btn:hover, .submit-btn:hover {
    background-color: #1a237e;
    transform: translateY(-2px);
}

/* Password Input */
.password-input {
    position: relative;
    width: 100%;
}

.password-input input {
    width: 100%;
    padding-right: 40px;
}

.toggle-password {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #666;
}

.terms-checkbox {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 20px 0;
}

.terms-checkbox input[type="checkbox"] {
    width: auto;
}

.btn-prev i, .btn-next i {
    margin: 0 5px;
}

.step i {
    margin-right: 5px;
}

/* Terms Link */
.terms-link {
    color: var(--primary-color);
    text-decoration: underline;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .registration-hero {
        height: 250px;
        background-attachment: scroll;
    }
    
    .registration-hero h1 {
        font-size: 2.5rem;
    }
    
    .registration-form-container {
        padding: 25px;
    }
    
    .radio-group, .checkbox-group {
        flex-direction: column;
        gap: 10px;
    }
    
    .form-buttons {
        flex-direction: column;
        gap: 15px;
    }
    
    .btn {
        width: 100%;
    }
}

/* Error states */
.form-group.error input {
    border-color: #e74c3c;
}

.error-message {
    color: #e74c3c;
    font-size: 0.9rem;
    margin-top: 0.3rem;
}

/* Error state improvements */
.form-group.error input,
.form-group.error select {
    border-color: #e74c3c;
}

.error-message {
    color: #e74c3c;
    font-size: 0.9rem;
    margin-top: 5px;
    display: none;
}

.form-group.error .error-message {
    display: block;
}

/* Success state */
.form-group.success input,
.form-group.success select {
    border-color: #2ecc71;
}

/* Add these new styles to your existing CSS */

.password-input {
    position: relative;
    width: 100%;
}

.password-input input {
    width: 100%;
    padding-right: 40px;
}

.toggle-password {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #666;
}

.terms-checkbox {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 20px 0;
}

.terms-checkbox input[type="checkbox"] {
    width: auto;
}

.btn-prev i, .btn-next i {
    margin: 0 5px;
}

.step i {
    margin-right: 5px;
}

/* Error state improvements */
.form-group.error input,
.form-group.error select {
    border-color: #e74c3c;
}

.error-message {
    color: #e74c3c;
    font-size: 0.9rem;
    margin-top: 5px;
    display: none;
}

.form-group.error .error-message {
    display: block;
}

/* Success state */
.form-group.success input,
.form-group.success select {
    border-color: #2ecc71;
}













