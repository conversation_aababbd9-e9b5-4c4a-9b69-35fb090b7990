// Preload critical images for better performance
function preloadCriticalImages() {
    const imagesToPreload = [
        'images/hero/main-hero.jpg',
        'images/cta/cta-background.jpg',
        'images/logos/cuk-full-logo.png'
    ];
    
    imagesToPreload.forEach(src => {
        const img = new Image();
        img.src = src;
    });
}

// Image lazy loading for non-critical images
function setupLazyLoading() {
    if ('loading' in HTMLImageElement.prototype) {
        // Browser supports native lazy loading
        const lazyImages = document.querySelectorAll('img[loading="lazy"]');
        lazyImages.forEach(img => {
            img.src = img.dataset.src;
        });
    } else {
        // Fallback for browsers that don't support native lazy loading
        const lazyImageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const lazyImage = entry.target;
                    lazyImage.src = lazyImage.dataset.src;
                    lazyImage.classList.remove('lazy');
                    lazyImageObserver.unobserve(lazyImage);
                }
            });
        });
        
        const lazyImages = document.querySelectorAll('.lazy');
        lazyImages.forEach(image => {
            lazyImageObserver.observe(image);
        });
    }
}

// Hero section parallax effect for all pages
function setupParallaxEffect() {
    // Target all hero sections across different pages
    const heroElements = document.querySelectorAll('.hero, .about-hero, .facilities-hero');
    
    if (heroElements.length > 0) {
        window.addEventListener('scroll', function() {
            // Only apply if not on mobile
            if (window.innerWidth > 768) {
                const scrollPosition = window.pageYOffset;
                
                // Apply to all hero elements
                heroElements.forEach(hero => {
                    // Adjust background position based on scroll
                    hero.style.backgroundPositionY = `${scrollPosition * 0.5}px`;
                });
            }
        });
    }
}

// Execute when DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    preloadCriticalImages();
    setupLazyLoading();
    setupParallaxEffect();
    
    // Add animation classes to elements when they come into view
    const animatedElements = document.querySelectorAll('.animate-on-scroll');
    
    const animationObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animated');
            }
        });
    }, { threshold: 0.1 });
    
    animatedElements.forEach(element => {
        animationObserver.observe(element);
    });
});

// Mobile menu toggle
const menuToggle = document.querySelector('.menu-toggle');
const navLinks = document.querySelector('.nav-links');
    
if (menuToggle) {
    menuToggle.addEventListener('click', function() {
        navLinks.classList.toggle('active');
        this.classList.toggle('active');
    });
}
    
// Close mobile menu when clicking outside
document.addEventListener('click', function(event) {
    if (navLinks && navLinks.classList.contains('active') && 
        !event.target.closest('.nav-links') && 
        !event.target.closest('.menu-toggle')) {
        navLinks.classList.remove('active');
        if (menuToggle) menuToggle.classList.remove('active');
    }
});
    
// Smooth scroll for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function(e) {
        const targetId = this.getAttribute('href');
        if (targetId === '#') return;
        
        const targetElement = document.querySelector(targetId);
        if (targetElement) {
            e.preventDefault();
            window.scrollTo({
                top: targetElement.offsetTop - 70, // Adjust for header height
                behavior: 'smooth'
            });
            
            // Close mobile menu after clicking a link
            if (navLinks && navLinks.classList.contains('active')) {
                navLinks.classList.remove('active');
                if (menuToggle) menuToggle.classList.remove('active');
            }
        }
    });
});
    
// Check if user is logged in
const token = localStorage.getItem('token');
if (token) {
    // Update navigation for logged-in users
    const loginLink = document.querySelector('.btn-login');
    const registerLink = document.querySelector('.btn-register');
    
    if (loginLink) {
        loginLink.textContent = 'Dashboard';
        loginLink.href = 'dashboard.html';
    }
    
    if (registerLink) {
        registerLink.textContent = 'Logout';
        registerLink.href = '#';
        registerLink.addEventListener('click', function(e) {
            e.preventDefault();
            localStorage.removeItem('token');
            window.location.href = 'index.html';
        });
    }
}







