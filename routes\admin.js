const express = require('express');
const router = express.Router();
const { requireAdmin } = require('../middleware/auth');
const pool = require('../config/database');

// Get attendance statistics
router.get('/attendance', requireAdmin, async (req, res) => {
    try {
        const [stats] = await pool.execute(`
            SELECT
                DATE(check_in) as date,
                COUNT(*) as total_visits,
                COUNT(DISTINCT student_id) as unique_visitors
            FROM attendance
            WHERE check_in >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY DATE(check_in)
            ORDER BY date DESC
            LIMIT 30
        `);

        res.json({
            success: true,
            stats
        });

    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Get member statistics
router.get('/members', auth(['admin']), async (req, res) => {
    try {
        const [stats] = await pool.execute(`
            SELECT 
                COUNT(*) as total_members,
                SUM(CASE WHEN membership_status = 'active' THEN 1 ELSE 0 END) as active_members,
                SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as new_members
            FROM users
            WHERE role = 'member'
        `);

        res.json({ stats: stats[0] });

    } catch (error) {
        console.error('Admin attendance stats error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch attendance statistics',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

// Get all students
router.get('/students', requireAdmin, async (req, res) => {
    try {
        const [students] = await pool.execute(`
            SELECT
                id, student_id, full_name, email, phone, gender,
                department, course, year, status, created_at
            FROM students
            ORDER BY created_at DESC
        `);

        res.json({
            success: true,
            students
        });

    } catch (error) {
        console.error('Admin students list error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch students',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

// Get dashboard statistics
router.get('/dashboard', requireAdmin, async (req, res) => {
    try {
        // Get total students
        const [totalStudents] = await pool.execute('SELECT COUNT(*) as count FROM students WHERE status = "active"');

        // Get today's attendance
        const [todayAttendance] = await pool.execute(`
            SELECT COUNT(*) as count
            FROM attendance
            WHERE DATE(check_in) = CURDATE()
        `);

        // Get active classes
        const [activeClasses] = await pool.execute('SELECT COUNT(*) as count FROM classes WHERE status = "active"');

        // Get recent registrations (last 7 days)
        const [recentRegistrations] = await pool.execute(`
            SELECT COUNT(*) as count
            FROM students
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        `);

        res.json({
            success: true,
            stats: {
                totalStudents: totalStudents[0].count,
                todayAttendance: todayAttendance[0].count,
                activeClasses: activeClasses[0].count,
                recentRegistrations: recentRegistrations[0].count
            }
        });

    } catch (error) {
        console.error('Admin dashboard error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch dashboard statistics',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

module.exports = router;
