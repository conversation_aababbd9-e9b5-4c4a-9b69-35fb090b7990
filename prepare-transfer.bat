@echo off
echo ========================================
echo   Preparing Project for Transfer
echo ========================================
echo.

echo 🧹 Cleaning up unnecessary files...
echo.

REM Remove node_modules (will be reinstalled on new laptop)
if exist "node_modules" (
    echo ❌ Removing node_modules folder...
    rmdir /s /q "node_modules"
    echo ✅ node_modules removed
) else (
    echo ℹ️  node_modules not found (already clean)
)

REM Remove temporary files
if exist "*.log" (
    echo ❌ Removing log files...
    del /q "*.log"
    echo ✅ Log files removed
)

REM Remove cache files
if exist ".cache" (
    echo ❌ Removing cache folder...
    rmdir /s /q ".cache"
    echo ✅ Cache removed
)

echo.
echo ✅ Project is ready for transfer!
echo.
echo 📦 Files to copy to new laptop:
echo    ✅ All .js, .html, .css files
echo    ✅ package.json & package-lock.json
echo    ✅ .env file (update MySQL password)
echo    ✅ All folders (css, js, routes, database, etc.)
echo    ❌ node_modules (will reinstall)
echo.
echo 🚀 On new laptop, run:
echo    1. npm install
echo    2. Update .env with new MySQL password
echo    3. npm run init-db
echo    4. npm run dev
echo.
pause
