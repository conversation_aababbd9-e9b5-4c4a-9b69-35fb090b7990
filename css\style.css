:root {
    --primary-color: #1a237e;
    --secondary-color: #e53935;
    --white: #ffffff;
    --light-bg: #f5f5f5;
    --dark-text: #333333;
    --card-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    line-height: 1.6;
    color: var(--dark-text);
}

/* Header and Navigation */
.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 5%;
    background-color: var(--white);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.logo {
    display: flex;
    align-items: center;
}

.nav-logo {
    height: 40px;
    margin-right: 10px;
}

.logo span {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.nav-links {
    display: flex;
    list-style: none;
}

.nav-links li {
    margin-left: 1.5rem;
}

.nav-links a {
    text-decoration: none;
    color: var(--dark-text);
    font-weight: 500;
    transition: color 0.3s;
}

.nav-links a:hover, .nav-links a.active {
    color: var(--primary-color);
}

.btn-login, .btn-register {
    padding: 8px 20px;
    border-radius: 4px;
    font-weight: 600;
}

.btn-login {
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-register {
    background-color: var(--secondary-color);
    color: var(--white);
}

.menu-toggle {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Hero Section with enhanced background image */
.hero {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: linear-gradient(rgba(26, 35, 126, 0.7), rgba(0, 0, 0, 0.8)),
                url('../images/hero/gym-hero-background.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed; /* Creates parallax effect */
    color: var(--white);
    margin-top: 60px;
    position: relative;
    overflow: hidden;
}

.hero:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, transparent 0%, rgba(0,0,0,0.4) 100%);
    z-index: 1;
}

.hero-content {
    max-width: 800px;
    padding: 0 20px;
    position: relative;
    z-index: 2;
    animation: fadeInUp 1.2s ease;
}

.hero h1 {
    font-size: 4rem;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 2px;
    text-shadow: 0 2px 10px rgba(0,0,0,0.3);
    font-weight: 800;
}

.hero-subtitle {
    font-size: 1.6rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    text-shadow: 0 2px 5px rgba(0,0,0,0.3);
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
}

.cta-button {
    padding: 15px 35px;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
    z-index: 1;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.cta-button:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.1);
    transition: all 0.4s ease;
    z-index: -1;
}

.cta-button:hover:before {
    left: 0;
}

.cta-button.primary {
    background-color: var(--secondary-color);
    color: var(--white);
}

.cta-button.primary:hover {
    background-color: #c0392b;
    transform: translateY(-3px);
}

.cta-button.secondary {
    background-color: transparent;
    border: 2px solid var(--white);
    color: var(--white);
}

.cta-button.secondary:hover {
    background-color: var(--white);
    color: var(--primary-color);
    transform: translateY(-3px);
}

/* Animation keyframes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Floating animation for hero elements */
.hero h1 {
    animation: float 6s ease-in-out infinite;
}

.hero-subtitle {
    animation: float 6s ease-in-out 0.5s infinite;
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .hero {
        background-attachment: scroll; /* Better performance on mobile */
    }
    
    .hero h1 {
        font-size: 2.8rem;
    }
    
    .hero-subtitle {
        font-size: 1.3rem;
    }
    
    .cta-buttons {
        flex-direction: column;
        gap: 15px;
    }
}

/* Features Section */
.features {
    padding: 5rem 5%;
    background-color: var(--light-bg);
    text-align: center;
}

.features h2 {
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: var(--primary-color);
    position: relative;
}

.features h2:after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background-color: var(--secondary-color);
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.feature-card {
    background-color: var(--white);
    padding: 30px;
    border-radius: 10px;
    box-shadow: var(--card-shadow);
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-10px);
}

.feature-icon {
    font-size: 3rem;
    color: var(--secondary-color);
    margin-bottom: 20px;
}

.feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: var(--primary-color);
}

/* Classes Preview Section */
.classes-preview {
    padding: 5rem 5%;
    text-align: center;
}

.classes-preview h2 {
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: var(--primary-color);
    position: relative;
}

.classes-preview h2:after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background-color: var(--secondary-color);
}

.class-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.class-card {
    background-color: var(--white);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--card-shadow);
    transition: transform 0.3s ease;
}

.class-card:hover {
    transform: translateY(-10px);
}

.class-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.class-info {
    padding: 20px;
}

.class-info h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.class-meta {
    display: flex;
    justify-content: space-between;
    margin: 15px 0;
    color: #777;
}

/* CTA Section */
.cta-section {
    padding: 5rem 5%;
    background: linear-gradient(rgba(0,0,0,0.8), rgba(0,0,0,0.8)),
                url('../images/cta-background.jpg');
    background-size: cover;
    background-position: center;
    text-align: center;
    color: var(--white);
}

.cta-content {
    max-width: 800px;
    margin: 0 auto;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Footer */
footer {
    background-color: #1a237e;
    color: var(--white);
    padding: 3rem 5% 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-bottom: 2rem;
}

.footer-section h3 {
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
    position: relative;
}

.footer-section h3:after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: var(--secondary-color);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-section a:hover {
    color: var(--white);
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background-color: rgba(255,255,255,0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s;
}

.social-links a:hover {
    background-color: var(--secondary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255,255,255,0.1);
}

/* Responsive design */
@media (max-width: 768px) {
    .menu-toggle {
        display: block;
    }
    
    .nav-links {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background-color: var(--white);
        flex-direction: column;
        align-items: center;
        padding: 2rem 0;
        transition: left 0.3s ease;
    }
    
    .nav-links.active {
        left: 0;
    }
    
    .nav-links li {
        margin: 1rem 0;
    }
    
    .hero h1 {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.2rem;
    }
    
    .cta-buttons {
        flex-direction: column;
        gap: 15px;
    }
    
    .feature-grid, .class-cards {
        grid-template-columns: 1fr;
    }
}

/* Animation for features */
.feature-card {
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-10px);
}

/* Hero section improvements */
.hero-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: #E3F2FD;
}

.hero-description {
    margin-bottom: 2rem;
}

/* Feature grid improvements */
.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    padding: 2rem;
}

/* Benefits section improvements */
.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    padding: 2rem;
}

.benefit-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
}

.benefit-card i {
    font-size: 2.5rem;
    color: #2196F3;
    margin-bottom: 1rem;
}

/* Login Page Styles */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 20px;
}

.login-box {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
}

.login-logo {
    display: block;
    margin: 0 auto 20px;
    max-width: 150px;
    height: auto;
}

.login-box h2 {
    text-align: center;
    color: #333;
    margin-bottom: 30px;
}

/* Login Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #555;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    color: #333; /* Ensure text is visible */
    background-color: #fff; /* Ensure background is white */
}

.password-input {
    position: relative;
}

.toggle-password {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #666;
}

.error-message {
    background-color: #ffebee;
    color: #d32f2f;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 20px;
    display: none;
}

.success-message {
    background-color: #e8f5e9;
    color: #2e7d32;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 20px;
    display: none;
}

.login-btn {
    width: 100%;
    padding: 12px;
    background-color: #4a90e2;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.login-btn:hover {
    background-color: #357abD;
}

.login-btn:disabled {
    background-color: #b0c4de;
    cursor: not-allowed;
}

.login-footer {
    margin-top: 20px;
    text-align: center;
    display: flex;
    justify-content: space-between;
}

.login-footer a {
    color: #007bff;
    text-decoration: none;
    font-size: 14px;
}

.login-footer a:hover {
    text-decoration: underline;
}

/* Particle effect styles */
.particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.particle {
    position: absolute;
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    animation: float-particle 15s infinite linear;
}

@keyframes float-particle {
    0% {
        transform: translateY(0) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100vh) rotate(360deg);
        opacity: 0;
    }
}



