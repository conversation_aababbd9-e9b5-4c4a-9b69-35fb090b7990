:root {
    --primary-color: #2c3e50;
    --secondary-color: #e74c3c;
    --accent-color: #3498db;
    --text-color: #333;
    --light-gray: #f5f6fa;
    --white: #ffffff;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
}

/* Navbar Styles */
.navbar {
    background-color: var(--white);
    padding: 1rem 5%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-logo {
    height: 40px;
}

.nav-links {
    display: flex;
    gap: 2rem;
    list-style: none;
}

.nav-links a {
    text-decoration: none;
    color: var(--text-color);
    font-weight: 500;
    transition: color 0.3s;
}

.btn-login, .btn-register {
    padding: 0.5rem 1.5rem;
    border-radius: 5px;
    font-weight: 600;
}

.btn-register {
    background-color: var(--secondary-color);
    color: var(--white) !important;
}

.btn-login {
    border: 2px solid var(--secondary-color);
    color: var(--secondary-color) !important;
}

/* Hero Section */
.hero {
    padding: 8rem 5% 4rem;
    background: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)),
                url('../images/gym-background.jpg');
    background-size: cover;
    background-position: center;
    color: var(--white);
    text-align: center;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 2rem;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

.cta-button {
    padding: 1rem 2rem;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 600;
    transition: transform 0.3s;
}

.cta-button:hover {
    transform: translateY(-3px);
}

.primary {
    background-color: var(--secondary-color);
    color: var(--white);
}

.secondary {
    background-color: transparent;
    border: 2px solid var(--white);
    color: var(--white);
}

/* Features Section */
.features {
    padding: 4rem 5%;
    background-color: var(--light-gray);
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.feature-card {
    background-color: var(--white);
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    font-size: 2.5rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

/* Schedule Section */
.schedule {
    padding: 4rem 5%;
}

.schedule-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.schedule-card {
    background-color: var(--light-gray);
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
}

/* Footer */
footer {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 3rem 5% 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section a {
    color: var(--white);
    text-decoration: none;
}

.social-links {
    display: flex;
    gap: 1rem;
    font-size: 1.5rem;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255,255,255,0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-links {
        display: none;
    }
    
    .menu-toggle {
        display: block;
        font-size: 1.5rem;
    }
    
    .hero h1 {
        font-size: 2rem;
    }
    
    .cta-buttons {
        flex-direction: column;
    }
}

/* Animation for features */
.feature-card {
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-10px);
}

/* Hero section improvements */
.hero-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: #E3F2FD;
}

.hero-description {
    margin-bottom: 2rem;
}

/* Feature grid improvements */
.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    padding: 2rem;
}

/* Benefits section improvements */
.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    padding: 2rem;
}

.benefit-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
}

.benefit-card i {
    font-size: 2.5rem;
    color: #2196F3;
    margin-bottom: 1rem;
}

/* Login Page Styles */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 20px;
}

.login-box {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
}

.login-logo {
    display: block;
    margin: 0 auto 20px;
    max-width: 150px;
    height: auto;
}

.login-box h2 {
    text-align: center;
    color: #333;
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #555;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    border-color: #007bff;
    outline: none;
}

.password-input {
    position: relative;
}

.toggle-password {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #666;
}

.login-btn {
    width: 100%;
    padding: 12px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.login-btn:hover {
    background: #0056b3;
}

.login-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 12px;
    border-radius: 4px;
    margin-bottom: 20px;
    display: none;
    text-align: center;
    border: 1px solid #f5c6cb;
}

.login-footer {
    margin-top: 20px;
    text-align: center;
    display: flex;
    justify-content: space-between;
}

.login-footer a {
    color: #007bff;
    text-decoration: none;
    font-size: 14px;
}

.login-footer a:hover {
    text-decoration: underline;
}


