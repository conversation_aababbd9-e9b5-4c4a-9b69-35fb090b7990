class AttendanceManager {
    constructor() {
        this.initializeElements();
        this.setupEventListeners();
        this.loadTodayAttendance();
    }

    initializeElements() {
        // Date picker - defaults to today
        this.datePicker = document.getElementById('attendanceDate');
        this.datePicker.valueAsDate = new Date();

        // Department filter
        this.departmentFilter = document.getElementById('departmentFilter');
        
        // Students table
        this.studentsTable = document.getElementById('studentsTableBody');
    }

    setupEventListeners() {
        // Refresh attendance when date changes
        this.datePicker.addEventListener('change', () => {
            this.loadAttendance(this.datePicker.value);
        });

        // Filter by department
        this.departmentFilter.addEventListener('change', () => {
            this.filterStudents(this.departmentFilter.value);
        });

        // Bulk actions
        document.getElementById('markAllPresent').addEventListener('click', () => {
            this.markAllStatus('present');
        });
    }

    async loadTodayAttendance() {
        try {
            const response = await fetch('/api/admin/students/attendance', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                }
            });

            if (!response.ok) throw new Error('Failed to load attendance');
            
            const students = await response.json();
            this.displayStudents(students);
        } catch (error) {
            console.error('Error:', error);
            alert('Failed to load attendance data');
        }
    }

    async markAttendance(studentId, status, remarks) {
        try {
            const response = await fetch('/api/admin/attendance', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify({
                    student_id: studentId,
                    attendance_date: this.datePicker.value,
                    attendance_status: status,
                    remarks: remarks
                })
            });

            if (!response.ok) throw new Error('Failed to mark attendance');

            // Show success feedback
            this.showSuccessMessage(`Attendance marked for student ${studentId}`);
            
            // Refresh the display
            await this.loadTodayAttendance();

        } catch (error) {
            console.error('Error:', error);
            this.showErrorMessage('Failed to mark attendance');
        }
    }

    showSuccessMessage(message) {
        const alert = document.createElement('div');
        alert.className = 'alert alert-success';
        alert.textContent = message;
        document.querySelector('.admin-content').prepend(alert);
        setTimeout(() => alert.remove(), 3000);
    }

    showErrorMessage(message) {
        const alert = document.createElement('div');
        alert.className = 'alert alert-error';
        alert.textContent = message;
        document.querySelector('.admin-content').prepend(alert);
        setTimeout(() => alert.remove(), 3000);
    }
}

// Initialize the attendance manager when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new AttendanceManager();
});

