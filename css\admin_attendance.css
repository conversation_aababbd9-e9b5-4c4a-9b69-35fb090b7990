.attendance-container {
    padding: 20px;
}

.attendance-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: #f5f5f5;
    border-radius: 8px;
}

.attendance-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.attendance-table th,
.attendance-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.attendance-status {
    padding: 6px;
    border-radius: 4px;
}

.status-present {
    background-color: #4CAF50;
    color: white;
}

.status-absent {
    background-color: #f44336;
    color: white;
}

.status-late {
    background-color: #ff9800;
    color: white;
}

.alert {
    padding: 10px;
    margin-bottom: 15px;
    border-radius: 4px;
}

.alert-success {
    background-color: #dff0d8;
    color: #3c763d;
    border: 1px solid #d6e9c6;
}

.alert-error {
    background-color: #f2dede;
    color: #a94442;
    border: 1px solid #ebccd1;
}
