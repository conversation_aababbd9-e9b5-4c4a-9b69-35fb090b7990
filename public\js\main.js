document.addEventListener('DOMContentLoaded', () => {
    // Check if user is logged in
    const token = localStorage.getItem('token');
    updateNavigation(token);

    // Event Listeners
    document.getElementById('loginLink').addEventListener('click', showLoginForm);
    document.getElementById('registerLink').addEventListener('click', showRegisterForm);
    document.getElementById('classesLink').addEventListener('click', loadClasses);
    document.getElementById('bookingsLink').addEventListener('click', loadBookings);
});

async function loadClasses() {
    try {
        const response = await fetch('/api/classes');
        const data = await response.json();
        displayClasses(data.classes);
    } catch (error) {
        console.error('Error loading classes:', error);
    }
}

function updateNavigation(token) {
    const nav = document.querySelector('.nav-links');
    if (token) {
        nav.innerHTML = `
            <a href="#" id="classesLink">Classes</a>
            <a href="#" id="bookingsLink">My Bookings</a>
            <a href="#" id="logoutLink">Logout</a>
        `;
    } else {
        nav.innerHTML = `
            <a href="#" id="classesLink">Classes</a>
            <a href="#" id="loginLink">Login</a>
            <a href="#" id="registerLink">Register</a>
        `;
    }
}
