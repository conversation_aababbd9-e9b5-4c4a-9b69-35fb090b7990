let currentStep = 1;

function validateStep(step) {
    const formStep = document.getElementById(`step${step}`);
    const inputs = formStep.querySelectorAll('input[required], select[required]');
    let isValid = true;

    inputs.forEach(input => {
        if (!input.value) {
            isValid = false;
            showError(input, 'This field is required');
        } else {
            clearError(input);
        }
    });

    if (step === 1) {
        const email = document.getElementById('email');
        const phone = document.getElementById('phone');
        
        if (!validateEmail(email.value)) {
            isValid = false;
            showError(email, 'Please enter a valid email address');
        }
        
        if (!validatePhone(phone.value)) {
            isValid = false;
            showError(phone, 'Please enter a valid phone number');
        }
    }

    if (step === 3) {
        const password = document.getElementById('password');
        const confirmPassword = document.getElementById('confirm-password');
        
        if (password.value !== confirmPassword.value) {
            isValid = false;
            showError(confirmPassword, 'Passwords do not match');
        }
        
        if (password.value.length < 8) {
            isValid = false;
            showError(password, 'Password must be at least 8 characters long');
        }
    }

    return isValid;
}

function showError(input, message) {
    const formGroup = input.closest('.form-group');
    formGroup.classList.add('error');
    
    let errorMessage = formGroup.querySelector('.error-message');
    if (!errorMessage) {
        errorMessage = document.createElement('div');
        errorMessage.className = 'error-message';
        formGroup.appendChild(errorMessage);
    }
    errorMessage.textContent = message;
}

function clearError(input) {
    const formGroup = input.closest('.form-group');
    formGroup.classList.remove('error');
    const errorMessage = formGroup.querySelector('.error-message');
    if (errorMessage) {
        errorMessage.remove();
    }
}

function validateEmail(email) {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
}

function validatePhone(phone) {
    return /^\d{10}$/.test(phone);
}

function nextStep(step) {
    if (validateStep(currentStep)) {
        document.getElementById(`step${currentStep}`).style.display = 'none';
        document.getElementById(`step${step}`).style.display = 'block';
        document.getElementById(`step${step}-indicator`).classList.add('active');
        currentStep = step;
    }
}

function previousStep(step) {
    document.getElementById(`step${currentStep}`).style.display = 'none';
    document.getElementById(`step${step}`).style.display = 'block';
    document.getElementById(`step${currentStep}-indicator`).classList.remove('active');
    currentStep = step;
}

function submitForm(event) {
    event.preventDefault();
    
    if (validateStep(currentStep)) {
        const formData = new FormData(event.target);
        const data = Object.fromEntries(formData);
        
        // Send registration data to server
        fetch('/api/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = '/registration-success.html';
            } else {
                alert(data.message || 'Registration failed. Please try again.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred. Please try again later.');
        });
    }
}

// Dynamic course loading based on department selection
document.getElementById('department').addEventListener('change', function() {
    const department = this.value;
    const courseSelect = document.getElementById('course');
    courseSelect.innerHTML = '<option value="">Select Course</option>';
    
    const courses = getCoursesByDepartment(department);
    courses.forEach(course => {
        const option = document.createElement('option');
        option.value = course.value;
        option.textContent = course.label;
        courseSelect.appendChild(option);
    });
});

function getCoursesByDepartment(department) {
    const coursesByDepartment = {
        computer_science: [
            { value: 'bsc_cs', label: 'B.Sc. Computer Science' },
            { value: 'btech_cs', label: 'B.Tech Computer Science' },
            { value: 'mca', label: 'Master of Computer Applications' }
        ],
        engineering: [
            { value: 'btech_mech', label: 'B.Tech Mechanical' },
            { value: 'btech_civil', label: 'B.Tech Civil' },
            { value: 'btech_electrical', label: 'B.Tech Electrical' }
        ],
        business: [
            { value: 'bba', label: 'Bachelor of Business Administration' },
            { value: 'mba', label: 'Master of Business Administration' }
        ],
        arts: [
            { value: 'ba_english', label: 'BA English' },
            { value: 'ba_psychology', label: 'BA Psychology' },
            { value: 'ba_economics', label: 'BA Economics' }
        ]
    };
    
    return coursesByDepartment[department] || [];
}

