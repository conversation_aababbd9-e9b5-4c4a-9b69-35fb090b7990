# 🏋️ University Gym Management System - Setup Guide

## 📋 Prerequisites

Before running the application, make sure you have:

1. **Node.js** (v14 or higher) - [Download here](https://nodejs.org/)
2. **MySQL** (v5.7 or higher) - [Download here](https://dev.mysql.com/downloads/)
3. **Git** (optional) - [Download here](https://git-scm.com/)

## 🚀 Quick Start (3 Steps)

### Step 1: Configure Database
1. Open the `.env` file in your project folder
2. Update the database password:
   ```
   DB_PASSWORD=your_mysql_password_here
   ```
   (Replace `your_mysql_password_here` with your actual MySQL password)

### Step 2: Start MySQL
Make sure your MySQL server is running:
- **Windows**: Start MySQL from Services or MySQL Workbench
- **Mac**: `brew services start mysql`
- **Linux**: `sudo service mysql start`

### Step 3: Run the Application
Double-click `start.bat` or run in terminal:
```bash
npm run dev
```

## 🌐 Access the Application

Once started, open your browser and go to:
- **Main Website**: http://localhost:3000
- **Admin Panel**: http://localhost:3000/admin/dashboard.html

## 🔐 Default Login Credentials

### Admin Login
- **Username**: admin
- **Password**: admin123
- **Email**: <EMAIL>

### Test Student (after registration)
You can register a new student or create test data.

## 📁 Project Structure

```
Major Project/
├── 📄 server.js              # Main server file
├── 📄 package.json           # Dependencies
├── 📄 .env                   # Environment configuration
├── 📄 start.bat              # Quick start script
├── 🗂️ admin/                 # Admin panel
│   └── dashboard.html
├── 🗂️ config/                # Database configuration
├── 🗂️ routes/                # API endpoints
├── 🗂️ middleware/            # Authentication
├── 🗂️ database/              # Database schema
├── 🗂️ scripts/               # Setup scripts
├── 🗂️ css/                   # Stylesheets
├── 🗂️ js/                    # Frontend JavaScript
└── 🗂️ images/                # Static images
```

## 🔧 Manual Setup (Alternative)

If the quick start doesn't work, follow these steps:

### 1. Install Dependencies
```bash
npm install
```

### 2. Setup Database
```bash
npm run init-db
```

### 3. Start Development Server
```bash
npm run dev
```

## 🎯 Features Available

### ✅ Working Features
- ✅ Student Registration
- ✅ Student/Admin Login
- ✅ Admin Dashboard
- ✅ Database Integration
- ✅ Responsive Design
- ✅ Security (JWT, Password Hashing)

### 🚧 Ready for Extension
- Class Booking System
- Attendance Tracking
- Payment Integration
- Email Notifications
- Advanced Reporting

## 🐛 Troubleshooting

### Database Connection Issues
1. **Check MySQL is running**
   ```bash
   # Windows
   net start mysql
   
   # Mac/Linux
   sudo service mysql start
   ```

2. **Verify credentials in .env file**
   - Check DB_USER (usually 'root')
   - Check DB_PASSWORD (your MySQL password)
   - Check DB_HOST (usually 'localhost')

3. **Test database connection**
   ```bash
   npm run init-db
   ```

### Port Already in Use
```bash
# Windows
netstat -ano | findstr :3000
taskkill /PID <PID> /F

# Mac/Linux
lsof -i :3000
kill -9 <PID>
```

### Module Not Found
```bash
rm -rf node_modules package-lock.json
npm install
```

## 📱 How to Use

### For Students
1. Go to http://localhost:3000
2. Click "Register" to create an account
3. Fill in the registration form
4. Login with your credentials
5. Access your dashboard

### For Administrators
1. Go to http://localhost:3000/admin/dashboard.html
2. Login with admin credentials
3. Manage students, classes, and facilities
4. View reports and analytics

## 🔄 Development Commands

```bash
npm start          # Production server
npm run dev        # Development server (auto-restart)
npm run init-db    # Initialize database
npm run setup      # Full setup (install + database)
```

## 📊 Database Schema

The system creates these tables automatically:
- `students` - Student information
- `admins` - Administrator accounts
- `attendance` - Check-in/check-out records
- `classes` - Fitness classes
- `class_bookings` - Class reservations
- `facilities` - Gym facilities

## 🔒 Security Features

- Password hashing with bcrypt
- JWT token authentication
- Input validation and sanitization
- SQL injection prevention
- CORS protection

## 📞 Support

If you encounter any issues:
1. Check this guide first
2. Look at the console for error messages
3. Verify all prerequisites are installed
4. Check the `.env` file configuration

## 🎉 Success!

If everything is working, you should see:
- ✅ Database connected successfully
- ✅ Server running on port 3000
- ✅ Admin panel accessible
- ✅ Student registration working

Your University Gym Management System is now ready to use!
