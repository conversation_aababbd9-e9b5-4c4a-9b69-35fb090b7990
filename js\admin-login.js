async function handleAdminLogin(event) {
    event.preventDefault();
    
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const errorMessage = document.getElementById('errorMessage');
    const loginButton = document.querySelector('.login-btn');

    // Basic validation
    if (!username || !password) {
        showError('Please fill in all fields');
        return false;
    }

    try {
        // Disable button and show loading state
        loginButton.disabled = true;
        loginButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Logging in...';

        const response = await fetch('/api/admin/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
        });

        // Check if response is ok before trying to parse JSON
        if (!response.ok) {
            const errorText = await response.text();
            try {
                const errorData = JSON.parse(errorText);
                throw new Error(errorData.error || 'Login failed');
            } catch (jsonError) {
                throw new Error('Login failed: Server error');
            }
        }

        // Try to parse the JSON response
        let data;
        try {
            data = await response.json();
        } catch (jsonError) {
            throw new Error('Invalid server response');
        }

        // Verify the response data has the expected properties
        if (!data.token || !data.user || !data.user.username) {
            throw new Error('Invalid server response format');
        }

        // Store the admin token and info
        localStorage.setItem('token', data.token);
        localStorage.setItem('isAdmin', 'true');
        localStorage.setItem('username', data.user.username);

        // Redirect to admin dashboard
        window.location.href = '/admin_dashboard.html';

    } catch (error) {
        showError(error.message || 'Login failed. Please try again.');
        console.error('Login error:', error);
    } finally {
        loginButton.disabled = false;
        loginButton.innerHTML = 'Login';
    }

    return false;
}

function showError(message) {
    const errorMessage = document.getElementById('errorMessage');
    errorMessage.textContent = message;
    errorMessage.style.display = 'block';
    setTimeout(() => {
        errorMessage.style.display = 'none';
    }, 5000);
}

// Check if already logged in
document.addEventListener('DOMContentLoaded', () => {
    const token = localStorage.getItem('token');
    const isAdmin = localStorage.getItem('isAdmin');
    
    if (token && isAdmin === 'true') {
        window.location.href = '/admin_dashboard.html';
    }
});


