const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');
require('dotenv').config();

async function initializeDatabase() {
    let connection;
    
    try {
        console.log('🔄 Initializing database...');
        
        // Connect to MySQL server (without specifying database)
        connection = await mysql.createConnection({
            host: process.env.DB_HOST || 'localhost',
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || '',
            port: process.env.DB_PORT || 3306,
            multipleStatements: true
        });
        
        console.log('✅ Connected to MySQL server');
        
        // Read and execute the setup SQL file
        const sqlFilePath = path.join(__dirname, '../database/setup.sql');
        const sqlContent = await fs.readFile(sqlFilePath, 'utf8');

        console.log('🔄 Executing database setup script...');

        // Split SQL content by semicolons and execute each statement
        const statements = sqlContent
            .split(';')
            .map(stmt => stmt.trim())
            .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

        for (const statement of statements) {
            if (statement.trim()) {
                try {
                    await connection.execute(statement);
                } catch (error) {
                    console.log(`⚠️  Warning: ${error.message}`);
                }
            }
        }
        
        console.log('✅ Database setup completed successfully!');
        console.log('📊 Database: gym_management');
        console.log('👤 Default admin credentials:');
        console.log('   Username: admin');
        console.log('   Password: admin123');
        console.log('   Email: <EMAIL>');
        
    } catch (error) {
        console.error('❌ Database initialization failed:', error.message);
        
        if (error.code === 'ER_ACCESS_DENIED_ERROR') {
            console.error('🔧 Please check your database credentials in .env file');
        } else if (error.code === 'ECONNREFUSED') {
            console.error('🔧 Please make sure MySQL server is running');
        }
        
        process.exit(1);
    } finally {
        if (connection) {
            await connection.end();
            console.log('🔌 Database connection closed');
        }
    }
}

// Run the initialization
if (require.main === module) {
    initializeDatabase();
}

module.exports = initializeDatabase;
