class AdminDashboard {
    constructor() {
        this.currentView = 'overview';
        this.init();
    }

    async init() {
        try {
            await this.checkAdminAuth();
            this.setupNavigation();
            this.loadOverviewData();
            this.setupEventListeners();
        } catch (error) {
            window.location.href = '/login.html';
        }
    }

    async checkAdminAuth() {
        const response = await fetch('/api/admin/verify', {
            credentials: 'include'
        });
        
        if (!response.ok) throw new Error('Not authorized');
    }

    setupNavigation() {
        const navItems = document.querySelectorAll('.admin-nav-item');
        navItems.forEach(item => {
            item.addEventListener('click', () => {
                this.switchView(item.dataset.view);
            });
        });
    }

    async switchView(view) {
        this.currentView = view;
        await this.loadViewData(view);
        this.updateActiveNav(view);
    }

    async loadViewData(view) {
        const handlers = {
            'overview': () => this.loadOverviewData(),
            'users': () => this.loadUsersData(),
            'attendance': () => this.loadAttendanceData(),
            'reports': () => this.loadReportsData()
        };

        if (handlers[view]) {
            await handlers[view]();
        }
    }

    async loadOverviewData() {
        try {
            const response = await fetch('/api/admin/dashboard-stats', {
                credentials: 'include'
            });
            
            if (!response.ok) throw new Error('Failed to load overview data');
            
            const data = await response.json();
            this.renderOverview(data);
        } catch (error) {
            this.showError('Failed to load dashboard statistics');
        }
    }

    renderOverview(data) {
        document.getElementById('totalUsers').textContent = data.totalUsers;
        document.getElementById('activeUsers').textContent = data.activeUsers;
        document.getElementById('todayAttendance').textContent = data.todayAttendance;
        
        this.renderUsageChart(data.usageStats);
        this.renderRecentActivities(data.recentActivities);
    }

    async loadUsersData() {
        try {
            const response = await fetch('/api/admin/users', {
                credentials: 'include'
            });
            
            if (!response.ok) throw new Error('Failed to load users data');
            
            const data = await response.json();
            this.renderUsersTable(data.users);
        } catch (error) {
            this.showError('Failed to load users data');
        }
    }

    renderUsersTable(users) {
        const tableBody = document.getElementById('usersTableBody');
        tableBody.innerHTML = users.map(user => `
            <tr>
                <td>${user.student_id}</td>
                <td>${user.full_name}</td>
                <td>${user.email}</td>
                <td>${user.department}</td>
                <td>
                    <button onclick="adminDashboard.editUser('${user.id}')" 
                            class="btn-edit">
                        Edit
                    </button>
                    <button onclick="adminDashboard.deleteUser('${user.id}')" 
                            class="btn-delete">
                        Delete
                    </button>
                </td>
            </tr>
        `).join('');
    }

    async editUser(userId) {
        try {
            const user = await this.fetchUserDetails(userId);
            this.showEditUserModal(user);
        } catch (error) {
            this.showError('Failed to load user details');
        }
    }

    async deleteUser(userId) {
        if (!confirm('Are you sure you want to delete this user?')) return;

        try {
            const response = await fetch(`/api/admin/users/${userId}`, {
                method: 'DELETE',
                credentials: 'include'
            });

            if (!response.ok) throw new Error('Failed to delete user');

            this.showNotification('User deleted successfully', 'success');
            await this.loadUsersData();
        } catch (error) {
            this.showError('Failed to delete user');
        }
    }

    showNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        setTimeout(() => notification.remove(), 3000);
    }

    showError(message) {
        this.showNotification(message, 'error');
    }
}

// Initialize admin dashboard
document.addEventListener('DOMContentLoaded', () => {
    window.adminDashboard = new AdminDashboard();
});
