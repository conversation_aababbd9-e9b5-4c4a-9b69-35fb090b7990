@echo off
echo ========================================
echo   FIXING UNIVERSITY GYM PROJECT
echo ========================================
echo.

echo [1] Checking system requirements...
echo.

echo Checking Node.js...
node --version
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found! Please install from https://nodejs.org/
    pause
    exit /b 1
)

echo Checking npm...
npm --version
if %errorlevel% neq 0 (
    echo ERROR: npm not found!
    pause
    exit /b 1
)

echo.
echo [2] Stopping any existing servers...
taskkill /IM node.exe /F >nul 2>&1
taskkill /IM nodemon.exe /F >nul 2>&1
echo Done.

echo.
echo [3] Installing project dependencies...
npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies!
    pause
    exit /b 1
)

echo.
echo [4] Checking MySQL connection...
mysql -u root -p797523@ -e "SELECT 1;" >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: MySQL connection failed. Please check:
    echo - MySQL is installed and running
    echo - Password is correct: 797523@
    echo.
    echo Continuing anyway...
)

echo.
echo [5] Creating database...
mysql -u root -p797523@ -e "CREATE DATABASE IF NOT EXISTS gym_management;" >nul 2>&1

echo.
echo [6] Starting the server...
echo.
echo ========================================
echo   PROJECT SHOULD BE RUNNING NOW
echo ========================================
echo.
echo Open your browser and go to:
echo   http://localhost:3004
echo.
echo Admin Login:
echo   Username: admin
echo   Password: admin123
echo.
echo To stop the server: Press Ctrl+C
echo.

npm run dev
