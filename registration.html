<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration - University Gym</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/registration.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="logo">
                <img src="images/cuk-full-logo.png" alt="UniGym Logo" class="nav-logo">
                <span>University GYM</span>
            </div>
            <ul class="nav-links">
                <li><a href="index.html">Home</a></li>
                <li><a href="facilities.html">Facilities</a></li>
                <li><a href="about.html">About</a></li>
                <li><a href="contact.html">Contact</a></li>
                <li><a href="login.html">Login</a></li>
                <li><a href="registration.html" class="active">Register</a></li>
            </ul>
            <div class="menu-toggle">
                <i class="fas fa-bars"></i>
            </div>
        </nav>
    </header>

    <main>
        <section class="registration-hero">
            <div class="hero-content">
                <h1>Join University Gym</h1>
                <p>Start your fitness journey today</p>
            </div>
        </section>

        <section class="registration-container">
            <div class="registration-form-container">
                <h2>Student Registration</h2>
                
                <!-- Progress Indicator -->
                <div class="form-progress">
                    <div class="progress-step active" id="step1-indicator">
                        <div class="step-number">1</div>
                        <div class="step-label">Personal Info</div>
                    </div>
                    <div class="progress-line"></div>
                    <div class="progress-step" id="step2-indicator">
                        <div class="step-number">2</div>
                        <div class="step-label">Academic Info</div>
                    </div>
                    <div class="progress-line"></div>
                    <div class="progress-step" id="step3-indicator">
                        <div class="step-number">3</div>
                        <div class="step-label">Health Info</div>
                    </div>
                    <div class="progress-line"></div>
                    <div class="progress-step" id="step4-indicator">
                        <div class="step-number">4</div>
                        <div class="step-label">Account Setup</div>
                    </div>
                </div>
                
                <form id="registrationForm" class="registration-form">
                    <div class="form-step active" id="step1">
                        <h3>Personal Information</h3>
                        
                        <div class="form-group">
                            <label for="studentId">Student ID*</label>
                            <input type="text" id="studentId" name="studentId" required>
                            <small>Enter your university student ID number</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="fullName">Full Name*</label>
                            <input type="text" id="fullName" name="fullName" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="email">Email Address*</label>
                            <input type="email" id="email" name="email" required>
                            <small>Please use your university email if possible</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="phone">Phone Number*</label>
                            <input type="tel" id="phone" name="phone" required>
                            <small>Format: 10-digit number without spaces or dashes</small>
                        </div>
                        
                        <div class="form-group">
                            <label>Gender*</label>
                            <div class="radio-group">
                                <label>
                                    <input type="radio" name="gender" value="male" required>
                                    Male
                                </label>
                                <label>
                                    <input type="radio" name="gender" value="female">
                                    Female
                                </label>
                                <label>
                                    <input type="radio" name="gender" value="other">
                                    Other
                                </label>
                            </div>
                        </div>
                        
                        <div class="form-buttons">
                            <button type="button" class="btn next-btn">Next <i class="fas fa-arrow-right"></i></button>
                        </div>
                    </div>
                    
                    <div class="form-step" id="step2">
                        <h3>Academic Information</h3>
                        
                        <div class="form-group">
                            <label for="department">Department*</label>
                            <input type="text" id="department" name="department" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="course">Course*</label>
                            <input type="text" id="course" name="course" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="year">Year of Study*</label>
                            <input type="number" id="year" name="year" required min="1" max="6">
                        </div>
                        
                        <div class="form-buttons">
                            <button type="button" class="btn prev-btn"><i class="fas fa-arrow-left"></i> Previous</button>
                            <button type="button" class="btn next-btn">Next <i class="fas fa-arrow-right"></i></button>
                        </div>
                    </div>
                    
                    <div class="form-step" id="step3">
                        <h3>Health Information</h3>
                        
                        <div class="form-group">
                            <label for="weight">Weight (kg)</label>
                            <input type="number" id="weight" name="weight" min="30" max="200">
                            <small>Optional: Used for fitness planning</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="height">Height (cm)</label>
                            <input type="number" id="height" name="height" min="100" max="250">
                            <small>Optional: Used for fitness planning</small>
                        </div>
                        
                        <div class="form-group">
                            <label>Do you have any medical conditions we should be aware of?</label>
                            <div class="radio-group">
                                <label>
                                    <input type="radio" name="medical" value="yes" id="medical-yes">
                                    Yes
                                </label>
                                <label>
                                    <input type="radio" name="medical" value="no" id="medical-no" checked>
                                    No
                                </label>
                            </div>
                        </div>
                        
                        <div class="form-group" id="medicalDescriptionGroup" style="display: none;">
                            <label for="medical-description">Please describe your medical condition</label>
                            <textarea id="medical-description" name="medical-description" rows="3"></textarea>
                            <small>This information will be kept confidential and only used for safety purposes</small>
                        </div>
                        
                        <div class="form-buttons">
                            <button type="button" class="btn prev-btn"><i class="fas fa-arrow-left"></i> Previous</button>
                            <button type="button" class="btn next-btn">Next <i class="fas fa-arrow-right"></i></button>
                        </div>
                    </div>
                    
                    <div class="form-step" id="step4">
                        <h3>Account Setup</h3>
                        
                        <div class="form-group">
                            <label for="password">Password*</label>
                            <div class="password-input">
                                <input type="password" id="password" name="password" required>
                                <i class="toggle-password fas fa-eye-slash" onclick="togglePasswordVisibility('password')"></i>
                            </div>
                            <small>Password must be at least 8 characters long and include at least one number</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="confirmPassword">Confirm Password*</label>
                            <div class="password-input">
                                <input type="password" id="confirmPassword" name="confirmPassword" required>
                                <i class="toggle-password fas fa-eye-slash" onclick="togglePasswordVisibility('confirmPassword')"></i>
                            </div>
                        </div>
                        
                        <div class="form-group checkbox-group">
                            <label>
                                <input type="checkbox" id="terms" name="terms" required>
                                I agree to the <a href="#" class="terms-link">Terms and Conditions</a>
                            </label>
                        </div>
                        
                        <div class="form-buttons">
                            <button type="button" class="btn prev-btn"><i class="fas fa-arrow-left"></i> Previous</button>
                            <button type="submit" class="btn submit-btn"><i class="fas fa-user-plus"></i> Register</button>
                        </div>
                    </div>
                </form>
            </div>
        </section>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-section">
                <h3>Contact Us</h3>
                <p>Email: <EMAIL></p>
                <p>Phone: (*************</p>
            </div>
            <div class="footer-section">
                <h3>Quick Links</h3>
                <ul>
                    <li><a href="facilities.html">Facilities</a></li>
                    <li><a href="schedule.html">Class Schedule</a></li>
                    <li><a href="membership.html">Membership</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h3>Follow Us</h3>
                <div class="social-links">
                    <a href="#"><i class="fab fa-facebook"></i></a>
                    <a href="#"><i class="fab fa-twitter"></i></a>
                    <a href="#"><i class="fab fa-instagram"></i></a>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2024 University Gym. All rights reserved.</p>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script src="js/registration.js"></script>
</body>
</html>






