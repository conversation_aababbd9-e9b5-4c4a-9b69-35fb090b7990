<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration - University Gym</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/registration.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
</head>
<body>
    <header>
        <!-- Same navbar as index.html -->
    </header>

    <main class="registration-container">
        <div class="registration-form">
            <h2>Gym Registration</h2>
            <div class="progress-bar">
                <div class="step active" id="step1-indicator">
                    <i class="fas fa-user"></i> Personal Info
                </div>
                <div class="step" id="step2-indicator">
                    <i class="fas fa-graduation-cap"></i> Academic Details
                </div>
                <div class="step" id="step3-indicator">
                    <i class="fas fa-heartbeat"></i> Health Info
                </div>
            </div>

            <form id="registrationForm" onsubmit="return submitForm(event)">
                <!-- Step 1: Personal Information -->
                <div class="form-step" id="step1">
                    <div class="form-group">
                        <label for="studentId">Student ID*</label>
                        <input type="text" id="studentId" name="studentId" placeholder="Enter your student ID" required>
                    </div>
                    <div class="form-group">
                        <label for="fullName">Full Name*</label>
                        <input type="text" id="fullName" name="fullName" placeholder="Enter your full name" required>
                    </div>
                    <div class="form-group">
                        <label for="email">Email ID*</label>
                        <input type="email" id="email" name="email" placeholder="Enter your university email" required>
                    </div>
                    <div class="form-group">
                        <label for="phone">Phone Number*</label>
                        <input type="tel" id="phone" name="phone" placeholder="+91 XXXXXXXXXX" required>
                    </div>
                    <div class="form-group">
                        <label>Gender*</label>
                        <div class="radio-group">
                            <input type="radio" id="male" name="gender" value="male" required>
                            <label for="male">Male</label>
                            <input type="radio" id="female" name="gender" value="female">
                            <label for="female">Female</label>
                        </div>
                    </div>
                    <button type="button" onclick="nextStep(2)" class="btn-next">Next <i class="fas fa-arrow-right"></i></button>
                </div>

                <!-- Step 2: Academic Details - Only these fields are editable by user -->
                <div class="form-step" id="step2" style="display: none;">
                    <div class="form-group">
                        <label for="department">Department*</label>
                        <input type="text" id="department" name="department" placeholder="Enter your department" required>
                    </div>
                    <div class="form-group">
                        <label for="course">Course*</label>
                        <input type="text" id="course" name="course" placeholder="Enter your course" required>
                    </div>
                    <div class="form-group">
                        <label for="year">Year of Study*</label>
                        <input type="number" id="year" name="year" min="1" max="6" placeholder="Enter your year of study" required>
                    </div>
                    <button type="button" onclick="previousStep(1)" class="btn-prev"><i class="fas fa-arrow-left"></i> Previous</button>
                    <button type="button" onclick="nextStep(3)" class="btn-next">Next <i class="fas fa-arrow-right"></i></button>
                </div>

                <!-- Step 3: Health Information -->
                <div class="form-step" id="step3" style="display: none;">
                    <div class="form-group">
                        <label for="weight">Weight (kg)</label>
                        <input type="number" id="weight" name="weight" min="30" max="200">
                    </div>
                    <div class="form-group">
                        <label for="height">Height (cm)</label>
                        <input type="number" id="height" name="height" min="100" max="250">
                    </div>
                    <div class="form-group">
                        <label>Any Medical Conditions?</label>
                        <div class="radio-group">
                            <input type="radio" id="medical-yes" name="medical" value="yes" onchange="toggleMedicalDetails()">
                            <label for="medical-yes">Yes</label>
                            <input type="radio" id="medical-no" name="medical" value="no" onchange="toggleMedicalDetails()">
                            <label for="medical-no">No</label>
                        </div>
                    </div>
                    <div class="form-group" id="medical-details" style="display: none;">
                        <label for="medical-description">Please specify medical conditions:</label>
                        <textarea id="medical-description" name="medical-description"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="password">Create Password*</label>
                        <div class="password-input">
                            <input type="password" id="password" name="password" required>
                            <i class="fas fa-eye-slash toggle-password" onclick="togglePasswordVisibility('password')"></i>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="confirm-password">Confirm Password*</label>
                        <div class="password-input">
                            <input type="password" id="confirm-password" name="confirm-password" required>
                            <i class="fas fa-eye-slash toggle-password" onclick="togglePasswordVisibility('confirm-password')"></i>
                        </div>
                    </div>
                    <div class="form-group terms-checkbox">
                        <input type="checkbox" id="terms" name="terms" required>
                        <label for="terms">I agree to the Terms and Conditions</label>
                    </div>
                    <button type="button" onclick="previousStep(2)" class="btn-prev"><i class="fas fa-arrow-left"></i> Previous</button>
                    <button type="submit" class="btn-submit">Submit Registration</button>
                </div>
            </form>
        </div>
    </main>

    <footer>
        <!-- Same footer as index.html -->
    </footer>

    <script src="js/validation.js"></script>
    <script src="js/registration.js"></script>
</body>
</html>

fetch('/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    student_id: "test",
    password: "password"
  })
})
.then(r => r.text())
.then(text => console.log(text));



