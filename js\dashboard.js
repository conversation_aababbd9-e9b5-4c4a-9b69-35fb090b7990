document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
    setupEventListeners();
    loadUserData();
    initializeCharts();
    updateCurrentTime();
    setInterval(updateCurrentTime, 60000); // Update time every minute
});

function initializeDashboard() {
    // Initialize all dashboard components
    const navLinks = document.querySelectorAll('.sidebar-nav a');
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = link.getAttribute('href').substring(1);
            showSection(targetId);
            updateActiveNav(link);
        });
    });

    // Show initial section (overview)
    showSection('overview');
}

function setupEventListeners() {
    // Sidebar toggle
    document.getElementById('sidebarToggle').addEventListener('click', () => {
        document.querySelector('.sidebar').classList.toggle('active');
    });

    // Quick action buttons
    document.getElementById('checkInBtn').addEventListener('click', handleCheckIn);
    document.getElementById('bookClassBtn').addEventListener('click', handleBookClass);
    document.getElementById('startWorkoutBtn').addEventListener('click', handleStartWorkout);

    // Profile form
    document.getElementById('profileForm').addEventListener('submit', handleProfileUpdate);
}

async function loadUserData() {
    try {
        const response = await fetch('/api/user/dashboard', {
            credentials: 'include'
        });

        if (!response.ok) throw new Error('Failed to load user data');

        const data = await response.json();
        updateDashboardData(data);
    } catch (error) {
        showNotification('Failed to load dashboard data', 'error');
    }
}

function updateDashboardData(data) {
    // Update user info
    document.getElementById('userName').textContent = data.user.firstName;
    document.getElementById('userFullName').textContent = data.user.fullName;
    
    // Update stats
    document.getElementById('todayWorkout').textContent = data.stats.todayWorkout;
    document.getElementById('caloriesBurned').textContent = data.stats.caloriesBurned;
    document.getElementById('workoutStreak').textContent = data.stats.streak;

    // Update schedule
    updateTodaySchedule(data.schedule);

    // Update activities
    updateRecentActivities(data.activities);
}

function initializeCharts() {
    const ctx = document.getElementById('workoutChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            datasets: [{
                label: 'Workout Duration (minutes)',
                data: [60, 45, 75, 60, 90, 30, 45],
                borderColor: '#1a237e',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
    });
    const dateString = now.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    document.getElementById('currentTime').textContent = `${dateString} ${timeString}`;
}

function showSection(sectionId) {
    document.querySelectorAll('.dashboard-section').forEach(section => {
        section.classList.remove('active');
    });
    document.getElementById(sectionId).classList.add('active');
}

function updateActiveNav(activeLink) {
    document.querySelectorAll('.sidebar-nav li').forEach(li => {
        li.classList.remove('active');
    });
    activeLink.parentElement.classList.add('active');
}

async function loadUserProfile() {
    try {
        const response = await fetch('/api/profile', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });
        
        if (response.ok) {
            const profile = await response.json();
            document.getElementById('profileFullName').textContent = profile.full_name;
            document.getElementById('profileStudentId').textContent = `Student ID: ${profile.student_id}`;
            document.getElementById('profileEmail').value = profile.email;
            document.getElementById('profilePhone').value = profile.phone;
            document.getElementById('profileDepartment').value = profile.department;
            document.getElementById('profileCourse').value = profile.course;
            
            // Update profile photo if exists
            if (profile.photo_url) {
                document.getElementById('profileImage').src = profile.photo_url;
            }
        }
    } catch (error) {
        console.error('Error loading profile:', error);
    }
}

function setupProfileForm() {
    const profileForm = document.getElementById('profileForm');
    profileForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        try {
            const response = await fetch('/api/profile', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify({
                    phone: document.getElementById('profilePhone').value,
                    email: document.getElementById('profileEmail').value
                })
            });

            if (response.ok) {
                alert('Profile updated successfully!');
            } else {
                throw new Error('Failed to update profile');
            }
        } catch (error) {
            alert('Error updating profile');
            console.error('Profile update error:', error);
        }
    });
}

async function loadAttendanceHistory() {
    try {
        const response = await fetch('/api/attendance/history', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        if (response.ok) {
            const history = await response.json();
            updateAttendanceTable(history);
        }
    } catch (error) {
        console.error('Error loading attendance history:', error);
    }
}

function updateAttendanceTable(history) {
    const tbody = document.getElementById('attendanceTableBody');
    tbody.innerHTML = history.map(record => `
        <tr>
            <td>${new Date(record.check_in).toLocaleDateString()}</td>
            <td>${new Date(record.check_in).toLocaleTimeString()}</td>
            <td>${record.check_out ? new Date(record.check_out).toLocaleTimeString() : '-'}</td>
            <td>${calculateDuration(record.check_in, record.check_out)}</td>
        </tr>
    `).join('');
}

function calculateDuration(checkIn, checkOut) {
    if (!checkOut) return '-';
    const duration = new Date(checkOut) - new Date(checkIn);
    const hours = Math.floor(duration / (1000 * 60 * 60));
    const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
}

async function handleCheckIn() {
    try {
        const response = await fetch('/api/attendance/check-in', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        if (response.ok) {
            alert('Check-in successful!');
            loadAttendanceHistory();
        } else {
            throw new Error('Check-in failed');
        }
    } catch (error) {
        alert('Error during check-in');
        console.error('Check-in error:', error);
    }
}

async function handleLogout() {
    try {
        // First, call the logout endpoint
        const response = await fetch('/api/auth/logout', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'Content-Type': 'application/json'
            }
        });

        // Whether the server request succeeds or fails, clear all local storage
        localStorage.clear();
        sessionStorage.clear();
        
        // Force redirect to student login page
        window.location.href = '/student-login.html';
        
    } catch (error) {
        console.error('Logout error:', error);
        // Even if there's an error, clear storage and redirect
        localStorage.clear();
        sessionStorage.clear();
        window.location.href = '/student-login.html';
    }
}

async function loadClassSchedule() {
    try {
        const response = await fetch('/api/schedule', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        if (response.ok) {
            const schedule = await response.json();
            displaySchedule(schedule);
        }
    } catch (error) {
        console.error('Error loading schedule:', error);
    }
}

function displaySchedule(schedule) {
    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'];
    days.forEach(day => {
        const daySlots = document.getElementById(`${day}Slots`);
        const daySchedule = schedule[day] || [];
        
        daySlots.innerHTML = daySchedule.map(slot => `
            <div class="class-slot">
                <h4>${slot.className}</h4>
                <p>${slot.time} - ${slot.instructor}</p>
                <p>${slot.availableSpots} spots available</p>
            </div>
        `).join('') || '<p>No classes scheduled</p>';
    });
}

// Initialize booking system
const bookingSystem = new BookingSystem();

// Add event listeners for booking-related buttons
document.getElementById('newBookingBtn').addEventListener('click', () => {
    bookingSystem.showBookingModal();
});

function setupPhotoUpload() {
    const photoInput = document.getElementById('photoUpload');
    const profileImage = document.getElementById('profileImage');

    photoInput.addEventListener('change', async (e) => {
        const file = e.target.files[0];
        if (!file) return;

        // Validate file type and size
        if (!file.type.match('image.*')) {
            alert('Please upload an image file');
            return;
        }

        if (file.size > 5 * 1024 * 1024) { // 5MB
            alert('File size should be less than 5MB');
            return;
        }

        const formData = new FormData();
        formData.append('photo', file);

        try {
            const response = await fetch('/api/profile/photo', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: formData
            });

            if (response.ok) {
                const data = await response.json();
                // Update the profile image
                profileImage.src = data.photoUrl;
                alert('Photo updated successfully!');
            } else {
                throw new Error('Failed to upload photo');
            }
        } catch (error) {
            console.error('Error uploading photo:', error);
            alert('Failed to upload photo. Please try again.');
        }
    });
}







