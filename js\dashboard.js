document.addEventListener('DOMContentLoaded', function() {
    // Check if user is logged in
    const user = sessionStorage.getItem('user');
    const token = localStorage.getItem('token');

    if (!user || !token) {
        window.location.href = '/login.html';
        return;
    }

    // Parse user data and display
    const userData = JSON.parse(user);
    document.getElementById('userName').textContent = userData.full_name || 'Student';
    document.getElementById('userFullName').textContent = userData.full_name || 'Student Name';

    initializeDashboard();
    setupEventListeners();
    initializeCharts();
    updateCurrentTime();
    setInterval(updateCurrentTime, 60000); // Update time every minute
});

function initializeDashboard() {
    // Initialize all dashboard components
    const navLinks = document.querySelectorAll('.sidebar-nav a');
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = link.getAttribute('href').substring(1);
            showSection(targetId);
            updateActiveNav(link);
        });
    });

    // Show initial section (overview)
    showSection('overview');
}

function setupEventListeners() {
    // Menu toggle
    const menuToggle = document.getElementById('menuToggle');
    if (menuToggle) {
        menuToggle.addEventListener('click', () => {
            document.querySelector('.sidebar').classList.toggle('active');
        });
    }

    // Check-in button
    const checkInBtn = document.getElementById('checkInBtn');
    if (checkInBtn) {
        checkInBtn.addEventListener('click', handleCheckIn);
    }

    // Logout buttons
    const logoutBtn = document.getElementById('logoutBtn');
    const logoutLink = document.getElementById('logoutLink');

    if (logoutBtn) {
        logoutBtn.addEventListener('click', handleLogout);
    }

    if (logoutLink) {
        logoutLink.addEventListener('click', (e) => {
            e.preventDefault();
            handleLogout();
        });
    }
}

async function loadUserData() {
    try {
        const response = await fetch('/api/user/dashboard', {
            credentials: 'include'
        });

        if (!response.ok) throw new Error('Failed to load user data');

        const data = await response.json();
        updateDashboardData(data);
    } catch (error) {
        showNotification('Failed to load dashboard data', 'error');
    }
}

function updateDashboardData(data) {
    // Update user info
    document.getElementById('userName').textContent = data.user.firstName;
    document.getElementById('userFullName').textContent = data.user.fullName;
    
    // Update stats
    document.getElementById('todayWorkout').textContent = data.stats.todayWorkout;
    document.getElementById('caloriesBurned').textContent = data.stats.caloriesBurned;
    document.getElementById('workoutStreak').textContent = data.stats.streak;

    // Update schedule
    updateTodaySchedule(data.schedule);

    // Update activities
    updateRecentActivities(data.activities);
}

function initializeCharts() {
    const ctx = document.getElementById('activityChart');
    if (ctx) {
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                datasets: [{
                    label: 'Workout Duration (minutes)',
                    data: [60, 45, 75, 60, 90, 30, 45],
                    borderColor: '#1a237e',
                    backgroundColor: 'rgba(26, 35, 126, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }
}

function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
    });
    const dateString = now.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    document.getElementById('currentTime').textContent = `${dateString} ${timeString}`;
}

function showSection(sectionId) {
    document.querySelectorAll('.dashboard-section').forEach(section => {
        section.classList.remove('active');
    });
    document.getElementById(sectionId).classList.add('active');
}

function updateActiveNav(activeLink) {
    document.querySelectorAll('.sidebar-nav li').forEach(li => {
        li.classList.remove('active');
    });
    activeLink.parentElement.classList.add('active');
}

async function loadUserProfile() {
    try {
        const response = await fetch('/api/profile', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });
        
        if (response.ok) {
            const profile = await response.json();
            document.getElementById('profileFullName').textContent = profile.full_name;
            document.getElementById('profileStudentId').textContent = `Student ID: ${profile.student_id}`;
            document.getElementById('profileEmail').value = profile.email;
            document.getElementById('profilePhone').value = profile.phone;
            document.getElementById('profileDepartment').value = profile.department;
            document.getElementById('profileCourse').value = profile.course;
            
            // Update profile photo if exists
            if (profile.photo_url) {
                document.getElementById('profileImage').src = profile.photo_url;
            }
        }
    } catch (error) {
        console.error('Error loading profile:', error);
    }
}

function setupProfileForm() {
    const profileForm = document.getElementById('profileForm');
    profileForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        try {
            const response = await fetch('/api/profile', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify({
                    phone: document.getElementById('profilePhone').value,
                    email: document.getElementById('profileEmail').value
                })
            });

            if (response.ok) {
                alert('Profile updated successfully!');
            } else {
                throw new Error('Failed to update profile');
            }
        } catch (error) {
            alert('Error updating profile');
            console.error('Profile update error:', error);
        }
    });
}

async function loadAttendanceHistory() {
    try {
        const response = await fetch('/api/attendance/history', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        if (response.ok) {
            const history = await response.json();
            updateAttendanceTable(history);
        }
    } catch (error) {
        console.error('Error loading attendance history:', error);
    }
}

function updateAttendanceTable(history) {
    const tbody = document.getElementById('attendanceTableBody');
    tbody.innerHTML = history.map(record => `
        <tr>
            <td>${new Date(record.check_in).toLocaleDateString()}</td>
            <td>${new Date(record.check_in).toLocaleTimeString()}</td>
            <td>${record.check_out ? new Date(record.check_out).toLocaleTimeString() : '-'}</td>
            <td>${calculateDuration(record.check_in, record.check_out)}</td>
        </tr>
    `).join('');
}

function calculateDuration(checkIn, checkOut) {
    if (!checkOut) return '-';
    const duration = new Date(checkOut) - new Date(checkIn);
    const hours = Math.floor(duration / (1000 * 60 * 60));
    const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
}

async function handleCheckIn() {
    try {
        const response = await fetch('/api/attendance/check-in', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            alert('Check-in successful!');
            // Update check-in status
            document.getElementById('checkInStatus').textContent = 'Checked In';
            document.getElementById('checkInBtn').textContent = 'Check Out';
        } else {
            throw new Error('Check-in failed');
        }
    } catch (error) {
        alert('Error during check-in: ' + error.message);
        console.error('Check-in error:', error);
    }
}

async function handleLogout() {
    try {
        // First, call the logout endpoint
        const response = await fetch('/api/auth/logout', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'Content-Type': 'application/json'
            }
        });

        // Whether the server request succeeds or fails, clear all local storage
        localStorage.clear();
        sessionStorage.clear();

        // Force redirect to login page
        window.location.href = '/login.html';

    } catch (error) {
        console.error('Logout error:', error);
        // Even if there's an error, clear storage and redirect
        localStorage.clear();
        sessionStorage.clear();
        window.location.href = '/login.html';
    }
}

async function loadClassSchedule() {
    try {
        const response = await fetch('/api/schedule', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        if (response.ok) {
            const schedule = await response.json();
            displaySchedule(schedule);
        }
    } catch (error) {
        console.error('Error loading schedule:', error);
    }
}

function displaySchedule(schedule) {
    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'];
    days.forEach(day => {
        const daySlots = document.getElementById(`${day}Slots`);
        const daySchedule = schedule[day] || [];
        
        daySlots.innerHTML = daySchedule.map(slot => `
            <div class="class-slot">
                <h4>${slot.className}</h4>
                <p>${slot.time} - ${slot.instructor}</p>
                <p>${slot.availableSpots} spots available</p>
            </div>
        `).join('') || '<p>No classes scheduled</p>';
    });
}

// Initialize booking system
const bookingSystem = new BookingSystem();

// Add event listeners for booking-related buttons
document.getElementById('newBookingBtn').addEventListener('click', () => {
    bookingSystem.showBookingModal();
});

function setupPhotoUpload() {
    const photoInput = document.getElementById('photoUpload');
    const profileImage = document.getElementById('profileImage');

    photoInput.addEventListener('change', async (e) => {
        const file = e.target.files[0];
        if (!file) return;

        // Validate file type and size
        if (!file.type.match('image.*')) {
            alert('Please upload an image file');
            return;
        }

        if (file.size > 5 * 1024 * 1024) { // 5MB
            alert('File size should be less than 5MB');
            return;
        }

        const formData = new FormData();
        formData.append('photo', file);

        try {
            const response = await fetch('/api/profile/photo', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: formData
            });

            if (response.ok) {
                const data = await response.json();
                // Update the profile image
                profileImage.src = data.photoUrl;
                alert('Photo updated successfully!');
            } else {
                throw new Error('Failed to upload photo');
            }
        } catch (error) {
            console.error('Error uploading photo:', error);
            alert('Failed to upload photo. Please try again.');
        }
    });
}







