function validateContactForm() {
    const email = document.getElementById('email').value;
    const message = document.getElementById('message').value;

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        alert("Please enter a valid email address!");
        return false;
    }

    // Message length validation
    if (message.length < 10) {
        alert("Message must be at least 10 characters long!");
        return false;
    }

    // If validation passes, show success message
    alert("Thank you for your message. We will get back to you soon!");
    return false; // Prevent actual form submission
}
