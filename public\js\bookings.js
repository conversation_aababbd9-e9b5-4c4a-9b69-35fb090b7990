class BookingSystem {
    constructor() {
        this.initializeEventListeners();
        this.loadAvailableClasses();
        this.loadUserBookings();
    }

    initializeEventListeners() {
        document.getElementById('refreshClasses')?.addEventListener('click', 
            () => this.loadAvailableClasses());
    }

    async loadAvailableClasses() {
        try {
            const response = await fetch('/api/bookings/available', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                }
            });

            if (!response.ok) throw new Error('Failed to load classes');
            
            const data = await response.json();
            this.displayAvailableClasses(data.classes);
        } catch (error) {
            this.showError('Failed to load available classes');
        }
    }

    displayAvailableClasses(classes) {
        const container = document.getElementById('availableClasses');
        if (!container) return;

        container.innerHTML = classes.map(classItem => `
            <div class="class-card ${classItem.available_slots <= 0 ? 'full' : ''}">
                <h3>${classItem.name}</h3>
                <p>${classItem.description}</p>
                <div class="class-details">
                    <span>Type: ${classItem.class_type}</span>
                    <span>Time: ${new Date(classItem.start_time).toLocaleString()}</span>
                    <span>Duration: ${classItem.duration} minutes</span>
                    <span>Available: ${classItem.available_slots}/${classItem.capacity}</span>
                </div>
                <button 
                    onclick="bookingSystem.bookClass(${classItem.id})"
                    ${classItem.available_slots <= 0 ? 'data-waitlist="true"' : ''}
                    class="book-button">
                    ${classItem.available_slots <= 0 ? 'Join Waitlist' : 'Book Now'}
                </button>
            </div>
        `).join('');
    }

    async bookClass(classId) {
        try {
            const response = await fetch('/api/bookings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify({ class_id: classId })
            });

            if (!response.ok) throw new Error('Booking failed');
            
            const data = await response.json();
            this.showSuccess(data.message);
            
            // Refresh both displays
            await this.loadAvailableClasses();
            await this.loadUserBookings();
        } catch (error) {
            this.showError(error.message);
        }
    }

    async loadUserBookings() {
        try {
            const response = await fetch('/api/bookings/my-bookings', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                }
            });

            if (!response.ok) throw new Error('Failed to load bookings');
            
            const data = await response.json();
            this.displayUserBookings(data.bookings);
        } catch (error) {
            this.showError('Failed to load your bookings');
        }
    }

    displayUserBookings(bookings) {
        const container = document.getElementById('userBookings');
        if (!container) return;

        container.innerHTML = bookings.length ? bookings.map(booking => `
            <div class="booking-card ${booking.status}">
                <h3>${booking.class_name}</h3>
                <div class="booking-details">
                    <span>Status: ${booking.status}</span>
                    <span>Date: ${new Date(booking.start_time).toLocaleDateString()}</span>
                    <span>Time: ${new Date(booking.start_time).toLocaleTimeString()}</span>
                </div>
            </div>
        `).join('') : '<p>No bookings found</p>';
    }

    showSuccess(message) {
        // Implement your preferred notification system
        alert(message);
    }

    showError(message) {
        // Implement your preferred notification system
        alert('Error: ' + message);
    }
}

// Initialize booking system when document is ready
document.addEventListener('DOMContentLoaded', () => {
    window.bookingSystem = new BookingSystem();
});