/* Login Form Specific Styles */
.form-container {
    max-width: 600px;
    margin: 3rem auto;
    padding: 2rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.form-container h2 {
    color: #2196F3;
    text-align: center;
    margin-bottom: 2rem;
    font-size: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus {
    border-color: #2196F3;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
    outline: none;
}

.radio-group {
    display: flex;
    gap: 2rem;
    padding: 0.5rem 0;
}

.radio-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.submit-btn {
    width: 100%;
    padding: 1rem;
    background: #2196F3;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.submit-btn:hover {
    background: #1976D2;
    transform: translateY(-2px);
}

/* Add required field indicator */
.form-group label::after {
    content: "*";
    color: #f44336;
    margin-left: 4px;
}

/* Add field icons */
.form-group {
    position: relative;
}

.form-group i {
    position: absolute;
    right: 12px;
    top: 38px;
    color: #757575;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .form-container {
        margin: 2rem;
        padding: 1.5rem;
    }
}

/* Success and Error Messages */
.message {
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
    display: none;
    text-align: center;
}

.success-message {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.error-message {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Password strength indicator */
.password-strength {
    margin-top: 0.5rem;
    height: 4px;
    background: #e0e0e0;
    border-radius: 2px;
}

.strength-meter {
    height: 100%;
    width: 0;
    border-radius: 2px;
    transition: all 0.3s ease;
}

.weak {
    width: 33.33%;
    background: #f44336;
}

.medium {
    width: 66.66%;
    background: #ffa726;
}

.strong {
    width: 100%;
    background: #66bb6a;
}

/* Terms and Conditions checkbox */
.terms-group {
    margin: 1.5rem 0;
}

.terms-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.terms-group input[type="checkbox"] {
    width: 18px;
    height: 18px;
}

/* Form steps progress */
.form-progress {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
    position: relative;
}

.progress-step {
    flex: 1;
    text-align: center;
    position: relative;
}

.step-number {
    width: 30px;
    height: 30px;
    background: #e0e0e0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.5rem;
    color: #757575;
    position: relative;
    z-index: 1;
}

.progress-step.active .step-number {
    background: #2196F3;
    color: white;
}

.progress-step.completed .step-number {
    background: #66bb6a;
    color: white;
}

.step-label {
    font-size: 0.9rem;
    color: #757575;
}

.progress-line {
    position: absolute;
    top: 15px;
    left: 0;
    right: 0;
    height: 2px;
    background: #e0e0e0;
    z-index: 0;
}

.success-content {
    text-align: center;
    padding: 30px;
    background-color: #d4edda;
    border-radius: 8px;
    margin: 20px 0;
    animation: fadeIn 0.5s ease-in;
}

.success-content i {
    font-size: 48px;
    color: #28a745;
    margin-bottom: 15px;
}

.success-content h3 {
    color: #155724;
    margin-bottom: 10px;
    font-size: 24px;
}

.success-content p {
    color: #155724;
    font-size: 16px;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}


