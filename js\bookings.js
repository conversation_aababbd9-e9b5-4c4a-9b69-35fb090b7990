class BookingSystem {
    constructor() {
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        const bookClassBtn = document.getElementById('bookClassBtn');
        if (bookClassBtn) {
            bookClassBtn.addEventListener('click', () => this.showBookingModal());
        }
    }

    async showBookingModal() {
        // Create and show booking modal
        const modal = document.createElement('div');
        modal.className = 'booking-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <h2>Book a Class</h2>
                <form id="bookingForm">
                    <select id="classType" required>
                        <option value="">Select Class Type</option>
                        <option value="yoga">Yoga</option>
                        <option value="cardio">Cardio</option>
                        <option value="strength">Strength Training</option>
                    </select>
                    <input type="date" id="classDate" required>
                    <select id="classTime" required>
                        <option value="">Select Time</option>
                        <option value="morning">Morning (6:00 AM)</option>
                        <option value="afternoon">Afternoon (2:00 PM)</option>
                        <option value="evening">Evening (6:00 PM)</option>
                    </select>
                    <button type="submit">Book Now</button>
                </form>
            </div>
        `;

        document.body.appendChild(modal);

        // Handle form submission
        const form = modal.querySelector('#bookingForm');
        form.addEventListener('submit', (e) => this.handleBooking(e, modal));
    }

    async handleBooking(e, modal) {
        e.preventDefault();
        
        const classType = document.getElementById('classType').value;
        const classDate = document.getElementById('classDate').value;
        const classTime = document.getElementById('classTime').value;

        try {
            const response = await fetch('/api/bookings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify({
                    classType,
                    classDate,
                    classTime
                })
            });

            if (response.ok) {
                alert('Booking successful!');
                modal.remove();
                // Refresh bookings display
                this.loadUserBookings();
            } else {
                throw new Error('Booking failed');
            }
        } catch (error) {
            alert('Error making booking. Please try again.');
            console.error('Booking error:', error);
        }
    }

    async loadUserBookings() {
        try {
            const response = await fetch('/api/bookings', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                }
            });

            if (response.ok) {
                const bookings = await response.json();
                // Display bookings
                this.displayBookings(bookings);
            } else {
                throw new Error('Failed to load bookings');
            }
        } catch (error) {
            console.error('Error loading bookings:', error);
        }
    }

    displayBookings(bookings) {
        const bookingsContainer = document.getElementById('bookingsContainer');
        bookingsContainer.innerHTML = ''; // Clear previous bookings

        if (bookings.length === 0) {
            bookingsContainer.innerHTML = '<p>No bookings found.</p>';
            return;
        }

        bookings.forEach(booking => {
            const bookingElement = document.createElement('div');
            bookingElement.className = 'booking';
            bookingElement.innerHTML = `
                <h3>${booking.classType}</h3>
                <p>Date: ${new Date(booking.classDate).toLocaleDateString()}</p>
                <p>Time: ${booking.classTime}</p>
            `;
            bookingsContainer.appendChild(bookingElement);
        });
    }
}
