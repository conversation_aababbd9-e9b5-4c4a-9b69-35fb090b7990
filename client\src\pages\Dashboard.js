class Dashboard {
    constructor() {
        this.user = null;
        this.attendanceChart = null;
        this.init();
    }

    async init() {
        try {
            await this.checkAuth();
            await this.loadUserData();
            this.setupEventListeners();
            this.initializeCharts();
        } catch (error) {
            console.error('Dashboard initialization failed:', error);
            window.location.href = '/login.html';
        }
    }

    async checkAuth() {
        const token = this.getCookie('token');
        if (!token) {
            throw new Error('No authentication token found');
        }
    }

    async loadUserData() {
        try {
            const response = await fetch('/api/users/dashboard', {
                credentials: 'include'
            });
            
            if (!response.ok) throw new Error('Failed to load dashboard data');
            
            const data = await response.json();
            this.user = data.user;
            this.renderDashboard(data);
        } catch (error) {
            console.error('Failed to load user data:', error);
            throw error;
        }
    }

    renderDashboard(data) {
        // Update profile section
        document.getElementById('userFullName').textContent = data.user.full_name;
        document.getElementById('userEmail').textContent = data.user.email;
        document.getElementById('userDepartment').textContent = data.user.department;

        // Update statistics
        this.updateStatistics(data.statistics);

        // Update attendance chart
        this.updateAttendanceChart(data.attendance);

        // Update recent activities
        this.updateRecentActivities(data.recentActivities);
    }

    updateStatistics(stats) {
        document.getElementById('totalVisits').textContent = stats.totalVisits;
        document.getElementById('averageTime').textContent = 
            this.formatDuration(stats.averageTimeSpent);
        document.getElementById('streakDays').textContent = stats.currentStreak;
    }

    updateAttendanceChart(attendance) {
        if (this.attendanceChart) {
            this.attendanceChart.destroy();
        }

        const ctx = document.getElementById('attendanceChart').getContext('2d');
        this.attendanceChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: attendance.dates,
                datasets: [{
                    label: 'Time Spent (minutes)',
                    data: attendance.durations,
                    borderColor: '#4CAF50',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    }

    updateRecentActivities(activities) {
        const container = document.getElementById('recentActivities');
        container.innerHTML = activities
            .map(activity => `
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="activity-details">
                        <span class="activity-time">
                            ${this.formatDate(activity.timestamp)}
                        </span>
                        <span class="activity-description">
                            ${activity.description}
                        </span>
                    </div>
                </div>
            `)
            .join('');
    }

    setupEventListeners() {
        document.getElementById('checkInBtn')?.addEventListener('click', 
            () => this.handleCheckIn());
        document.getElementById('checkOutBtn')?.addEventListener('click', 
            () => this.handleCheckOut());
    }

    async handleCheckIn() {
        try {
            const response = await fetch('/api/attendance/check-in', {
                method: 'POST',
                credentials: 'include'
            });
            
            if (!response.ok) throw new Error('Check-in failed');
            
            const data = await response.json();
            this.showNotification('Check-in successful!', 'success');
            await this.loadUserData(); // Refresh dashboard data
        } catch (error) {
            this.showNotification('Check-in failed. Please try again.', 'error');
        }
    }

    showNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        setTimeout(() => notification.remove(), 3000);
    }

    formatDate(date) {
        return new Date(date).toLocaleDateString('en-US', {
            day: 'numeric',
            month: 'short',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    formatDuration(minutes) {
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        return `${hours}h ${mins}m`;
    }

    getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
    }
}

// Initialize dashboard
document.addEventListener('DOMContentLoaded', () => {
    new Dashboard();
});
