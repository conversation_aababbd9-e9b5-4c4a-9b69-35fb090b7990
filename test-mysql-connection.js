const mysql = require('mysql2/promise');
require('dotenv').config();

async function testMySQLConnection() {
    console.log('🔍 Testing MySQL Connection...');
    console.log('=====================================');
    
    // Display current configuration
    console.log('📋 Current Configuration:');
    console.log(`   Host: ${process.env.DB_HOST || 'localhost'}`);
    console.log(`   User: ${process.env.DB_USER || 'root'}`);
    console.log(`   Password: ${process.env.DB_PASSWORD ? '***hidden***' : 'empty'}`);
    console.log(`   Database: ${process.env.DB_NAME || 'gym_management'}`);
    console.log(`   Port: ${process.env.DB_PORT || 3306}`);
    console.log('');

    try {
        // Test 1: Connect to MySQL server (without database)
        console.log('🔄 Step 1: Testing MySQL server connection...');
        const serverConnection = await mysql.createConnection({
            host: process.env.DB_HOST || 'localhost',
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || '',
            port: process.env.DB_PORT || 3306
        });
        
        console.log('✅ MySQL server connection successful!');
        
        // Test 2: Check if database exists
        console.log('🔄 Step 2: Checking if database exists...');
        const [databases] = await serverConnection.execute('SHOW DATABASES');
        const dbExists = databases.some(db => db.Database === (process.env.DB_NAME || 'gym_management'));
        
        if (dbExists) {
            console.log('✅ Database "gym_management" exists!');
        } else {
            console.log('⚠️  Database "gym_management" does not exist yet');
            console.log('   This is normal - it will be created when you run: npm run init-db');
        }
        
        await serverConnection.end();
        
        // Test 3: Test full connection (if database exists)
        if (dbExists) {
            console.log('🔄 Step 3: Testing full database connection...');
            const dbConnection = await mysql.createConnection({
                host: process.env.DB_HOST || 'localhost',
                user: process.env.DB_USER || 'root',
                password: process.env.DB_PASSWORD || '',
                database: process.env.DB_NAME || 'gym_management',
                port: process.env.DB_PORT || 3306
            });
            
            // Test query
            const [result] = await dbConnection.execute('SELECT 1 + 1 AS result');
            console.log('✅ Database query test successful!');
            console.log(`   Test result: ${result[0].result}`);
            
            await dbConnection.end();
        }
        
        console.log('');
        console.log('🎉 MySQL Connection Test PASSED!');
        console.log('');
        console.log('🚀 Next steps:');
        console.log('   1. Run: npm install');
        console.log('   2. Run: npm run init-db');
        console.log('   3. Run: npm run dev');
        
    } catch (error) {
        console.log('');
        console.log('❌ MySQL Connection Test FAILED!');
        console.log('');
        console.log('🔧 Error Details:');
        console.log(`   ${error.message}`);
        console.log('');
        
        // Provide specific troubleshooting
        if (error.code === 'ECONNREFUSED') {
            console.log('🔧 Troubleshooting:');
            console.log('   ❌ MySQL server is not running');
            console.log('   ✅ Solutions:');
            console.log('      - Start MySQL service');
            console.log('      - Windows: Services → MySQL → Start');
            console.log('      - Mac: brew services start mysql');
            console.log('      - Linux: sudo service mysql start');
        } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
            console.log('🔧 Troubleshooting:');
            console.log('   ❌ Wrong username or password');
            console.log('   ✅ Solutions:');
            console.log('      - Check DB_USER in .env file (usually "root")');
            console.log('      - Check DB_PASSWORD in .env file');
            console.log('      - Try: mysql -u root -p (to test manually)');
        } else if (error.code === 'ENOTFOUND') {
            console.log('🔧 Troubleshooting:');
            console.log('   ❌ Cannot find MySQL server');
            console.log('   ✅ Solutions:');
            console.log('      - Check DB_HOST in .env file (usually "localhost")');
            console.log('      - Make sure MySQL is installed');
        }
        
        console.log('');
        console.log('📖 For more help, see:');
        console.log('   - QUICK_START.md');
        console.log('   - MySQL installation guide');
    }
}

// Run the test
testMySQLConnection();
