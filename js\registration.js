function togglePasswordVisibility(fieldId) {
    const passwordField = document.getElementById(fieldId);
    const icon = passwordField.nextElementSibling;
    
    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        icon.classList.replace('fa-eye-slash', 'fa-eye');
    } else {
        passwordField.type = 'password';
        icon.classList.replace('fa-eye', 'fa-eye-slash');
    }
}

function toggleMedicalDetails() {
    const medicalDetails = document.getElementById('medical-details');
    const hasMedicalConditions = document.getElementById('medical-yes').checked;
    medicalDetails.style.display = hasMedicalConditions ? 'block' : 'none';
}

// Remove updateCourses function since we're using text inputs now

// Add these functions to handle form navigation and validation
let currentStep = 1;

function nextStep(step) {
    if (validateStep(currentStep)) {
        document.getElementById(`step${currentStep}`).style.display = 'none';
        document.getElementById(`step${step}`).style.display = 'block';
        document.getElementById(`step${currentStep}-indicator`).classList.remove('active');
        document.getElementById(`step${step}-indicator`).classList.add('active');
        currentStep = step;
    }
}

function previousStep(step) {
    document.getElementById(`step${currentStep}`).style.display = 'none';
    document.getElementById(`step${step}`).style.display = 'block';
    document.getElementById(`step${currentStep}-indicator`).classList.remove('active');
    document.getElementById(`step${step}-indicator`).classList.add('active');
    currentStep = step;
}

function validateStep(step) {
    let isValid = true;

    // Get all required inputs in the current step (step is 0-indexed)
    const stepNumber = step + 1;
    const inputs = document.querySelectorAll(`#step${stepNumber} [required]`);

    // Clear previous errors
    inputs.forEach(input => clearError(input));

    // Validate each required field
    inputs.forEach(input => {
        if (!input.value.trim()) {
            showError(input, 'This field is required');
            isValid = false;
        }
    });
    
    // Step-specific validations (step is 0-indexed)
    if (step === 0) {
        const email = document.getElementById('email');
        const phone = document.getElementById('phone');

        if (email.value && !validateEmail(email.value)) {
            showError(email, 'Please enter a valid email address');
            isValid = false;
        }

        if (phone.value && !validatePhone(phone.value)) {
            showError(phone, 'Please enter a valid 10-digit phone number');
            isValid = false;
        }
    }

    if (step === 1) {
        // Validate department, course, and year
        const year = document.getElementById('year');
        if (year.value && (year.value < 1 || year.value > 6)) {
            showError(year, 'Year must be between 1 and 6');
            isValid = false;
        }
    }

    if (step === 3) {
        const password = document.getElementById('password');
        const confirmPassword = document.getElementById('confirmPassword');

        if (password.value && password.value.length < 6) {
            showError(password, 'Password must be at least 6 characters');
            isValid = false;
        }

        if (password.value !== confirmPassword.value) {
            showError(confirmPassword, 'Passwords do not match');
            isValid = false;
        }
    }
    
    return isValid;
}

function submitForm(event) {
    event.preventDefault();
    
    if (validateStep(currentStep)) {
        // Show loading state
        const submitButton = document.querySelector('.btn-submit');
        const originalButtonText = submitButton.innerHTML;
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Submitting...';
        
        const formData = new FormData(event.target);
        const data = Object.fromEntries(formData);
        
        // Log the data being sent (for debugging)
        console.log('Sending registration data:', data);
        
        // Send registration data to server
        fetch('/api/auth/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
            credentials: 'include' // Include cookies if needed for session
        })
        .then(response => {
            // First check if response exists
            if (!response) {
                throw new Error('No response from server');
            }
            
            // Log the response status
            console.log('Server response status:', response.status);
            
            // Try to get the response text first
            return response.text().then(text => {
                // Log the raw response
                console.log('Raw server response:', text);
                
                // Try to parse it as JSON
                try {
                    return text ? JSON.parse(text) : {};
                } catch (e) {
                    console.error('JSON Parse Error:', e);
                    throw new Error('Invalid server response format');
                }
            });
        })
        .then(data => {
            if (data.success) {
                // Show success message before redirecting
                alert('Registration successful! Redirecting to home page...');
                window.location.href = '/index.html'; // Changed to redirect to home page
            } else {
                // Show specific error message from server
                alert(data.message || 'Registration failed. Please check your information and try again.');
            }
        })
        .catch(error => {
            console.error('Registration error:', error);
            
            // Show more specific error message
            if (error.message.includes('Failed to fetch')) {
                alert('Unable to connect to the server. Please check your internet connection and try again.');
            } else {
                alert('Registration error: ' + error.message);
            }
        })
        .finally(() => {
            // Reset button state
            submitButton.disabled = false;
            submitButton.innerHTML = originalButtonText;
        });
    }
}

function showError(input, message) {
    const formGroup = input.closest('.form-group');
    formGroup.classList.add('error');
    
    let errorMessage = formGroup.querySelector('.error-message');
    if (!errorMessage) {
        errorMessage = document.createElement('div');
        errorMessage.className = 'error-message';
        formGroup.appendChild(errorMessage);
    }
    errorMessage.textContent = message;
}

function clearError(input) {
    const formGroup = input.closest('.form-group');
    formGroup.classList.remove('error');
    const errorMessage = formGroup.querySelector('.error-message');
    if (errorMessage) {
        errorMessage.remove();
    }
}

function validateEmail(email) {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
}

function validatePhone(phone) {
    return /^\d{10}$/.test(phone);
}

document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('registrationForm');
    const steps = document.querySelectorAll('.form-step');
    const nextButtons = document.querySelectorAll('.next-btn');
    const prevButtons = document.querySelectorAll('.prev-btn');
    const submitButton = document.querySelector('.submit-btn');
    const medicalRadios = document.querySelectorAll('input[name="medical"]');
    const medicalDescriptionGroup = document.getElementById('medicalDescriptionGroup');
    
    // Initialize form
    let currentStep = 0;
    showStep(currentStep);
    
    // Next button event listeners
    nextButtons.forEach(button => {
        button.addEventListener('click', () => {
            if (validateStep(currentStep)) {
                currentStep++;
                showStep(currentStep);
            }
        });
    });
    
    // Previous button event listeners
    prevButtons.forEach(button => {
        button.addEventListener('click', () => {
            currentStep--;
            showStep(currentStep);
        });
    });
    
    // Medical condition radio buttons
    medicalRadios.forEach(radio => {
        radio.addEventListener('change', () => {
            if (radio.value === 'yes') {
                medicalDescriptionGroup.style.display = 'block';
            } else {
                medicalDescriptionGroup.style.display = 'none';
            }
        });
    });

    // Form submission
    form.addEventListener('submit', submitForm);
    
    // Show the current step
    function showStep(step) {
        steps.forEach((s, index) => {
            s.style.display = index === step ? 'block' : 'none';
        });
        
        // Toggle visibility of navigation buttons
        prevButtons.forEach(btn => btn.style.display = step === 0 ? 'none' : 'block');
        nextButtons.forEach(btn => btn.style.display = step === steps.length - 1 ? 'none' : 'block');
        submitButton.style.display = step === steps.length - 1 ? 'block' : 'none';
    }
});







