const express = require('express');
const router = express.Router();
const DbUtils = require('../utils/db-utils');

router.get('/test', async (req, res) => {
    try {
        const result = await DbUtils.executeQuery('SELECT 1 + 1 AS result');
        res.json({ success: true, data: result });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

module.exports = router;
