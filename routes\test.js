const express = require('express');
const router = express.Router();
const pool = require('../config/database');

router.get('/test', async (req, res) => {
    try {
        const [result] = await pool.execute('SELECT 1 + 1 AS result');
        res.json({
            success: true,
            message: 'Database connection successful',
            data: result
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Database connection failed',
            error: error.message
        });
    }
});

module.exports = router;
