require('dotenv').config();

const config = {
    app: {
        port: process.env.PORT || 3000,
        env: process.env.NODE_ENV || 'development',
        jwtSecret: process.env.JWT_SECRET,
        jwtExpiry: '24h',
        corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:3000'
    },
    db: {
        host: process.env.DB_HOST,
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME,
        port: process.env.DB_PORT || 3306,
        connectionLimit: 10
    },
    email: {
        host: process.env.SMTP_HOST,
        port: process.env.SMTP_PORT,
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
            user: process.env.SMTP_USER,
            pass: process.env.SMTP_PASS
        }
    },
    security: {
        bcryptRounds: 12,
        rateLimitWindow: 15 * 60 * 1000, // 15 minutes
        rateLimitMax: 100 // requests per window
    }
};

module.exports = config;
