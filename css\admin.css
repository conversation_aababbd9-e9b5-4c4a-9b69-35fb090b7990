.attendance-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1rem;
    background: #f5f5f5;
    border-radius: 8px;
}

.students-list table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.students-list th,
.students-list td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.students-list th {
    background-color: #f5f5f5;
    font-weight: 600;
}

.attendance-status {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    width: 100%;
}

.remarks-input {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    width: 100%;
}

.btn-mark {
    padding: 0.5rem 1rem;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.btn-mark:hover {
    background-color: #45a049;
}

tr.marked {
    animation: flash 2s;
}

@keyframes flash {
    0% { background-color: #4CAF50; }
    100% { background-color: transparent; }
}

/* Admin Panel Comprehensive Styles */
:root {
    --admin-primary: #1a237e;
    --admin-secondary: #e74c3c;
    --admin-accent: #3498db;
    --admin-success: #2ecc71;
    --admin-warning: #f39c12;
    --admin-danger: #e74c3c;
    --admin-light: #f8f9fa;
    --admin-dark: #2c3e50;
    --admin-border: #dee2e6;
    --admin-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.admin-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Styles */
.admin-sidebar {
    width: 260px;
    background: var(--admin-primary);
    color: white;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    transition: all 0.3s ease;
    z-index: 1000;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.admin-logo {
    width: 40px;
    height: 40px;
    object-fit: contain;
}

.sidebar-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
}

.sidebar-nav ul {
    list-style: none;
    padding: 1rem 0;
}

.sidebar-nav li {
    margin: 0.25rem 0;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.sidebar-nav a:hover {
    background: rgba(255,255,255,0.1);
    color: white;
}

.sidebar-nav li.active a {
    background: rgba(255,255,255,0.1);
    color: white;
    border-left-color: var(--admin-secondary);
}

.sidebar-nav a i {
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
}

.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1rem;
    border-top: 1px solid rgba(255,255,255,0.1);
}

.logout-btn {
    width: 100%;
    padding: 0.75rem;
    background: var(--admin-secondary);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.logout-btn:hover {
    background: #c0392b;
}

/* Main Content */
.admin-main {
    flex: 1;
    margin-left: 260px;
    transition: margin-left 0.3s ease;
}

.admin-header {
    background: white;
    padding: 1rem 2rem;
    box-shadow: var(--admin-shadow);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 999;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sidebar-toggle {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    display: none;
}

.header-left h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--admin-dark);
}

.admin-user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--admin-dark);
}

.admin-content {
    padding: 2rem;
}

.admin-section {
    display: none;
}

.admin-section.active {
    display: block;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: var(--admin-shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.stat-card:nth-child(1) .stat-icon {
    background: rgba(52, 152, 219, 0.1);
    color: var(--admin-accent);
}

.stat-card:nth-child(2) .stat-icon {
    background: rgba(46, 204, 113, 0.1);
    color: var(--admin-success);
}

.stat-card:nth-child(3) .stat-icon {
    background: rgba(243, 156, 18, 0.1);
    color: var(--admin-warning);
}

.stat-card:nth-child(4) .stat-icon {
    background: rgba(231, 76, 60, 0.1);
    color: var(--admin-danger);
}

.stat-info h3 {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.stat-info p {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--admin-dark);
}

/* Section Header */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.section-header h2 {
    font-size: 1.5rem;
    color: var(--admin-dark);
}

/* Buttons */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.btn-primary {
    background: var(--admin-accent);
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
}

.btn-success {
    background: var(--admin-success);
    color: white;
}

.btn-warning {
    background: var(--admin-warning);
    color: white;
}

.btn-danger {
    background: var(--admin-danger);
    color: white;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* Table Styles */
.table-container {
    background: white;
    border-radius: 10px;
    box-shadow: var(--admin-shadow);
    overflow: hidden;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
}

.admin-table th,
.admin-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--admin-border);
}

.admin-table th {
    background: var(--admin-light);
    font-weight: 600;
    color: var(--admin-dark);
}

.admin-table tbody tr:hover {
    background: #f8f9fa;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-badge.active {
    background: rgba(46, 204, 113, 0.1);
    color: var(--admin-success);
}

.status-badge.inactive {
    background: rgba(231, 76, 60, 0.1);
    color: var(--admin-danger);
}

/* Cards */
.class-card,
.facility-card {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: var(--admin-shadow);
    margin-bottom: 1rem;
}

.class-card h4,
.facility-card h4 {
    margin-bottom: 1rem;
    color: var(--admin-dark);
}

.card-actions {
    margin-top: 1rem;
    display: flex;
    gap: 0.5rem;
}

/* Loading and No Data */
.loading-card,
.no-data {
    text-align: center;
    padding: 2rem;
    color: #666;
    font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
    }

    .admin-sidebar.active {
        transform: translateX(0);
    }

    .admin-main {
        margin-left: 0;
    }

    .sidebar-toggle {
        display: block;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
}
