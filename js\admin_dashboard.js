class AdminDashboard {
    constructor() {
        this.currentSection = 'dashboard';
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.checkAuth();
        this.loadDashboardData();
        this.setCurrentDate();
    }

    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.sidebar-nav a').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = e.target.closest('a').dataset.section;
                this.showSection(section);
            });
        });

        // Sidebar toggle
        document.querySelector('.sidebar-toggle')?.addEventListener('click', () => {
            document.querySelector('.admin-sidebar').classList.toggle('collapsed');
        });
    }

    checkAuth() {
        const token = localStorage.getItem('token');
        const user = JSON.parse(sessionStorage.getItem('user') || '{}');

        if (!token || !user.role || !['admin', 'super_admin'].includes(user.role)) {
            window.location.href = '/login.html';
            return;
        }

        const adminNameElement = document.getElementById('admin-name');
        if (adminNameElement) {
            adminNameElement.textContent = user.full_name || 'Admin User';
        }
    }

    showSection(sectionName) {
        // Hide all sections
        document.querySelectorAll('.admin-section').forEach(section => {
            section.classList.remove('active');
        });

        // Show selected section
        const targetSection = document.getElementById(sectionName);
        if (targetSection) {
            targetSection.classList.add('active');
        }

        // Update navigation
        document.querySelectorAll('.sidebar-nav li').forEach(li => {
            li.classList.remove('active');
        });
        const navLink = document.querySelector(`[data-section="${sectionName}"]`);
        if (navLink) {
            navLink.closest('li').classList.add('active');
        }

        // Update page title
        const titles = {
            dashboard: 'Dashboard',
            students: 'Student Management',
            attendance: 'Attendance Records',
            classes: 'Class Management',
            facilities: 'Facility Management',
            reports: 'Reports & Analytics'
        };
        const titleElement = document.getElementById('page-title');
        if (titleElement) {
            titleElement.textContent = titles[sectionName] || sectionName;
        }

        this.currentSection = sectionName;
        this.loadSectionData(sectionName);
    }

    async loadDashboardData() {
        try {
            const response = await this.apiCall('/api/admin/dashboard');
            if (response.success) {
                this.updateDashboardStats(response.stats);
            }
        } catch (error) {
            console.error('Failed to load dashboard data:', error);
            this.showError('Failed to load dashboard statistics');
        }
    }

    updateDashboardStats(stats) {
        const elements = {
            'total-students': stats.totalStudents || 0,
            'today-attendance': stats.todayAttendance || 0,
            'active-classes': stats.activeClasses || 0,
            'new-registrations': stats.recentRegistrations || 0
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });
    }

    async loadSectionData(section) {
        switch (section) {
            case 'students':
                await this.loadStudents();
                break;
            case 'attendance':
                await this.loadAttendance();
                break;
            case 'classes':
                await this.loadClasses();
                break;
            case 'facilities':
                await this.loadFacilities();
                break;
        }
    }

    async loadStudents() {
        try {
            const response = await this.apiCall('/api/admin/students');
            if (response.success) {
                this.renderStudentsTable(response.students);
            }
        } catch (error) {
            console.error('Failed to load students:', error);
            this.showError('Failed to load students');
        }
    }

    renderStudentsTable(students) {
        const tbody = document.getElementById('students-table-body');
        if (!tbody) return;

        if (!students || students.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="no-data">No students found</td></tr>';
            return;
        }

        tbody.innerHTML = students.map(student => `
            <tr>
                <td>${student.student_id}</td>
                <td>${student.full_name}</td>
                <td>${student.email}</td>
                <td>${student.department}</td>
                <td>
                    <span class="status-badge ${student.status}">${student.status}</span>
                </td>
                <td>
                    <button class="btn btn-sm btn-info" onclick="viewStudent('${student.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="editStudent('${student.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    async loadAttendance() {
        try {
            const response = await this.apiCall('/api/admin/attendance');
            if (response.success) {
                this.renderAttendanceData(response.stats);
            }
        } catch (error) {
            console.error('Failed to load attendance:', error);
            this.showError('Failed to load attendance data');
        }
    }

    renderAttendanceData(stats) {
        // Update attendance summary
        const today = new Date().toISOString().split('T')[0];
        const todayStats = stats.find(stat => stat.date === today);

        const checkinsElement = document.getElementById('checkins-today');
        const currentlyInElement = document.getElementById('currently-in-gym');

        if (checkinsElement) {
            checkinsElement.textContent = todayStats?.total_visits || 0;
        }
        if (currentlyInElement) {
            currentlyInElement.textContent = todayStats?.unique_visitors || 0;
        }
    }

    async loadClasses() {
        const grid = document.getElementById('classes-grid');
        if (!grid) return;

        grid.innerHTML = '<div class="loading-card">Loading classes...</div>';

        // Simulate loading classes
        setTimeout(() => {
            grid.innerHTML = `
                <div class="class-card">
                    <h4>Morning Yoga</h4>
                    <p>Instructor: Sarah Johnson</p>
                    <p>Time: 07:00 - 08:00</p>
                    <p>Capacity: 15/20</p>
                    <div class="card-actions">
                        <button class="btn btn-sm btn-primary">Edit</button>
                        <button class="btn btn-sm btn-danger">Delete</button>
                    </div>
                </div>
                <div class="class-card">
                    <h4>HIIT Training</h4>
                    <p>Instructor: Mike Wilson</p>
                    <p>Time: 18:00 - 18:45</p>
                    <p>Capacity: 8/12</p>
                    <div class="card-actions">
                        <button class="btn btn-sm btn-primary">Edit</button>
                        <button class="btn btn-sm btn-danger">Delete</button>
                    </div>
                </div>
            `;
        }, 1000);
    }

    async loadFacilities() {
        const grid = document.getElementById('facilities-grid');
        if (!grid) return;

        grid.innerHTML = '<div class="loading-card">Loading facilities...</div>';

        // Simulate loading facilities
        setTimeout(() => {
            grid.innerHTML = `
                <div class="facility-card">
                    <h4>Weight Training Area</h4>
                    <p>Capacity: 30 people</p>
                    <p>Status: Available</p>
                    <div class="card-actions">
                        <button class="btn btn-sm btn-primary">Edit</button>
                        <button class="btn btn-sm btn-warning">Maintenance</button>
                    </div>
                </div>
                <div class="facility-card">
                    <h4>Cardio Section</h4>
                    <p>Capacity: 25 people</p>
                    <p>Status: Available</p>
                    <div class="card-actions">
                        <button class="btn btn-sm btn-primary">Edit</button>
                        <button class="btn btn-sm btn-warning">Maintenance</button>
                    </div>
                </div>
            `;
        }, 1000);
    }

    async apiCall(endpoint, options = {}) {
        const token = localStorage.getItem('token');

        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            credentials: 'include'
        };

        const response = await fetch(endpoint, { ...defaultOptions, ...options });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    }

    showError(message) {
        // Simple error display - you can enhance this
        console.error(message);
        alert(message);
    }

    setCurrentDate() {
        const today = new Date().toISOString().split('T')[0];
        const dateInput = document.getElementById('attendance-date');
        if (dateInput) {
            dateInput.value = today;
        }
    }
}

// Global functions for button actions
function refreshStudents() {
    if (window.adminDashboard) {
        window.adminDashboard.loadStudents();
    }
}

function viewStudent(id) {
    alert(`View student ${id} - Feature coming soon!`);
}

function editStudent(id) {
    alert(`Edit student ${id} - Feature coming soon!`);
}

function filterAttendance() {
    const date = document.getElementById('attendance-date')?.value;
    alert(`Filter attendance for ${date} - Feature coming soon!`);
}

function addClass() {
    alert('Add new class - Feature coming soon!');
}

function addFacility() {
    alert('Add new facility - Feature coming soon!');
}

function generateReport() {
    const reportType = document.getElementById('report-type')?.value;
    alert(`Generate ${reportType} report - Feature coming soon!`);
}

function logout() {
    if (confirm('Are you sure you want to logout?')) {
        localStorage.removeItem('token');
        sessionStorage.removeItem('user');
        window.location.href = '/login.html';
    }
}

// Initialize admin dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.adminDashboard = new AdminDashboard();
});
